I'll update the Project Structure document to include all the screens you've mentioned. Let me analyze the existing structure and add the missing parts.

# Card Management System - Project Structure and Architecture (Updated)

## Overview

This document outlines the architecture and structure for a React-based Card Management System application. Based on the UI mockups, this is an admin dashboard that allows users to manage digital cards, create card templates, view analytics, manage card applications, and handle user management.

## Tech Stack

### Core Technologies
- **Frontend Framework**: React with TypeScript
- **Routing**: React Router v6
- **Styling**: Tailwind CSS
- **State Management**: Zustand (with mock data)
- **UI Component Library**: Chakra UI
- **Authentication**: Mock authentication in Zustand store
- **Form Handling**: React Hook Form with Zod validation

### Development Tools
- **Package Manager**: npm
- **Build Tool**: Vite
- **Linting**: ESLint with TypeScript config
- **Formatting**: Prettier

## Project Structure

```
card-management-system/
├── public/                     # Static assets
├── src/
│   ├── routes/                 # React Router routes
│   │   ├── root.tsx            # Root layout route
│   │   ├── login.tsx           # Login page
│   │   ├── dashboard.tsx       # Dashboard view
│   │   ├── application/        # Application related routes
│   │   │   ├── view-applications.tsx # View applications 
│   │   │   └── view-details.tsx     # View application details
│   │   ├── users/              # User management routes
│   │   │   ├── view-users.tsx    # View users list
│   │   │   ├── create-user.tsx   # Create user form
│   │   │   └── edit-user.tsx     # Edit user form
│   │   ├── cards/              # Card management routes
│   │   │   ├── view-cards.tsx    # View cards list
│   │   │   ├── create-card.tsx   # Create card form
│   │   │   └── edit-card.tsx     # Edit card form
│   │   ├── rules/              # Rule management routes
│   │   │   ├── view-templates.tsx # View rule templates
│   │   │   ├── create-template.tsx # Create template form
│   │   │   └── edit-template.tsx   # Edit template form
│   │   └── index.tsx           # Main routes configuration
│   ├── components/             # Reusable components
│   │   ├── layout/             # Layout components
│   │   │   ├── Sidebar.tsx     # Sidebar component
│   │   │   ├── Header.tsx      # Header component
│   │   │   ├── MainLayout.tsx  # Main layout wrapper
│   │   │   └── ...             # Other layout components
│   │   ├── cards/              # Card-specific components
│   │   │   ├── CardList.tsx    # Card list component
│   │   │   ├── CardForm.tsx    # Card form component
│   │   │   ├── CardPreview.tsx # Card preview component
│   │   │   └── ...             # Other card components
│   │   ├── templates/          # Template-specific components
│   │   │   ├── TemplateForm.tsx # Template form component
│   │   │   ├── TemplatePreview.tsx # Template preview component
│   │   │   └── ...             # Other template components
│   │   ├── users/              # User-specific components
│   │   │   ├── UserList.tsx    # User list component
│   │   │   ├── UserForm.tsx    # User form component
│   │   │   └── ...             # Other user components
│   │   ├── applications/       # Application-specific components
│   │   │   ├── ApplicationList.tsx # Application list component
│   │   │   ├── ApplicationDetails.tsx # Application details component
│   │   │   └── ...             # Other application components
│   │   └── dashboard/          # Dashboard-specific components
│   │       ├── StatsCard.tsx   # Statistics card component
│   │       ├── Chart.tsx       # Chart component
│   │       └── ...             # Other dashboard components
│   ├── hooks/                  # Custom hooks
│   │   ├── useAuth.ts          # Authentication hook
│   │   ├── useCardData.ts      # Cards data hook
│   │   ├── useTemplateData.ts  # Templates data hook
│   │   ├── useUserData.ts      # Users data hook
│   │   └── ...                 # Other hooks
│   ├── utils/                  # Utility functions
│   │   ├── formatters.ts       # Data formatters
│   │   ├── validators.ts       # Form validators
│   │   ├── helpers.ts          # Helper functions
│   │   └── ...                 # Other utilities
│   ├── types/                  # TypeScript type definitions
│   │   ├── card.ts             # Card type definitions
│   │   ├── template.ts         # Template type definitions
│   │   ├── user.ts             # User type definitions
│   │   ├── application.ts      # Application type definitions
│   │   └── ...                 # Other type definitions
│   ├── store/                  # Zustand stores with mock data
│   │   ├── authStore.ts        # Authentication store with mock auth
│   │   ├── cardStore.ts        # Cards store with mock card data
│   │   ├── templateStore.ts    # Templates store with mock templates
│   │   ├── applicationStore.ts # Applications store with mock applications
│   │   ├── userStore.ts        # Users store with mock user data
│   │   ├── dashboardStore.ts   # Dashboard store with mock statistics
│   │   └── index.ts            # Store exports
│   ├── api/                    # API types (no actual API calls)
│   │   └── types.ts            # Shared type definitions for API data models
│   ├── theme/                  # Chakra UI theme customization
│   │   ├── index.ts            # Theme entry point
│   │   ├── components/         # Component theme overrides
│   │   ├── foundations.ts      # Theme foundations
│   │   └── ...                 # Other theme files
│   └── styles/                 # Global styles
│       ├── index.css           # Global CSS
│       └── ...                 # Other styles
├── .eslintrc.js                # ESLint configuration
├── .prettierrc                 # Prettier configuration
├── tsconfig.json               # TypeScript configuration
├── vite.config.ts              # Vite configuration
├── tailwind.config.js          # Tailwind CSS configuration
├── package.json                # NPM package configuration
└── README.md                   # Project documentation
```

## Main Views

Based on the mockups, the application includes the following primary views:

### 1. Login
- User authentication form
- Error handling for invalid credentials
- Password reset functionality

### 2. Dashboard
- Overview page with statistics and charts
- Performance metrics visualization
- Quick access to important functions

### 3. Application Management
- **View Applications**: List of card applications with  sorting
- **View Details**: Detailed view of individual application

### 4. User Management
- **View Users**: List of system users with Employee Code and Status
- **Create User**: Form for adding new users to the system
- **Edit User**: Form for modifying existing user information and permissions

### 5. Card Management
- **View Cards**: List of existing cards with thumbnails and actions (Activate, Edit Details)
- **Create Card**: Form for creating new cards with template selection and preview
- **Edit Card**: Form for editing existing cards with live preview

### 6. Rule Management
- **View Templates**: List of rule templates
- **Create Template**: Interface for creating new rule templates with field configuration
- **Edit Template**: Interface for modifying existing templates with field management

## Component Architecture

### Core Components

#### Layout Components
- **Sidebar**: Navigation menu with links to different sections (using Chakra UI's Drawer component)
- **Header**: App header with user profile, notifications, and app title

#### Card Components
- **CardItem**: Displays a card with image, title, and actions
- **CardList**: Grid or list of CardItems with pagination
- **CardForm**: Form for creating or editing cards
- **CardPreview**: Visual preview of how the card will appear

#### Template Components
- **TemplateForm**: Interface for creating and editing templates
- **FieldEditor**: Component for adding and configuring fields in templates
- **TemplatePreview**: Preview of the template with sample data

#### User Components
- **UserList**: Table or grid of users with roles and status
- **UserForm**: Form for creating or editing user information
- **RoleSelector**: Component for assigning roles and permissions

#### Application Components
- **ApplicationList**: List of applications with status indicators
- **ApplicationFilter**: Interface for filtering applications
- **ApplicationDetails**: Detailed view of application information
- **StatusManager**: Component for updating application status

#### Dashboard Components
- **StatsCard**: Card displaying a single statistic with icon (using Chakra UI's Stat component)
- **Chart**: Visualization component using Recharts
- **StatusIndicator**: Visual indicator of status (success, pending, etc.)
- **FilterBar**: Interface for filtering data in lists

#### Chakra UI Components
Leveraging Chakra UI components as the base UI library:
- **Button**: Using Chakra UI Button with custom styling
- **Input**: Using Chakra form controls
- **Select**: Using Chakra Select component
- **Table**: Using Chakra Table components
- **Modal**: Using Chakra Modal components
- **Toast**: Using Chakra Toast system
- **Tabs**: Using Chakra Tabs components
- **Box, Flex, Grid**: For layout components
- **Badge**: For status indicators

### Data Flow and State Management

The application will use Zustand for state management with embedded mock data:

1. **Zustand Stores with Mocked Data**:
   - **authStore**: Manages authentication state, user info, permissions, and mock login functionality
   - **cardStore**: Handles card data, card operations, and related state with mock card data
   - **templateStore**: Manages template data and operations with mock template data
   - **applicationStore**: Manages application data with mock application records
   - **userStore**: Manages user data with mock user records
   - **dashboardStore**: Provides mock dashboard statistics and chart data

2. **Benefits of This Approach**:
   - Simple, lightweight API
   - No need for external mocking libraries
   - TypeScript support
   - Persistence via Zustand's persist middleware
   - Easy to replace with real API calls later
   - Simulated async behavior with loading states

3. **Component-Level State**:
   - Form values and validation
   - UI interactions (open/closed states, selected items)
   - Temporary data

## API Integration (Mocked in Stores)

Instead of using external mocking libraries like MSW, the application will simulate API interactions directly within the Zustand stores:

1. **Mock Data Approach**:
   - Each store will contain its own mock data
   - Simulated API delays using setTimeout to mimic network requests
   - Proper error handling and loading states

2. **Mock Implementation Strategy**:
   - Define shared type definitions in `src/api/types.ts`
   - Implement CRUD operations in Zustand stores with simulated delays
   - Handle error cases and edge conditions
   - Use Zustand's persist middleware for persisting data between sessions

3. **API Operations To Mock**:
   - **Authentication**: Login, logout, session management
   - **Cards**: CRUD operations, status updates
   - **Templates**: CRUD operations
   - **Applications**: List, view, approve, reject
   - **Users**: CRUD operations, role management
   - **Dashboard**: Statistics and chart data

4. **Example Store Implementation**:
   ```typescript
   // Example cardStore with mocked API
   export const useCardStore = create<CardState>((set, get) => ({
     cards: [...mockCards], // Initial mock data
     isLoading: false,
     error: null,
     
     fetchCards: async () => {
       set({ isLoading: true });
       // Simulate API delay
       await new Promise(resolve => setTimeout(resolve, 800));
       set({ isLoading: false });
       // Mock data is already loaded in initial state
     },
     
     // Other CRUD operations with simulated delays
   }));
   ```

5. **Benefits of This Approach**:
   - Simplified development setup
   - No network interception or service worker configuration
   - Full control over mock data behavior
   - Easy to transition to real API later by updating store implementations
   - Maintains realistic loading and error states

## Responsive Design Strategy

The application will follow a responsive design approach:

1. **Mobile First**: Core functionality designed for mobile first
2. **Breakpoints**:
   - Small (< 640px): Mobile view
   - Medium (640px - 768px): Small tablets
   - Large (768px - 1024px): Tablets and small laptops
   - Extra Large (> 1024px): Desktops and large screens

3. **Layout Adjustments**:
   - Sidebar collapses to a bottom navigation on mobile
   - Cards stack vertically on smaller screens
   - Tables convert to card views on mobile
   - Forms adjust field layout based on screen width

## Performance Optimization

1. **Code Splitting**: Using React.lazy and Suspense for code splitting
2. **Lazy Loading**: Components and routes loaded as needed
3. **Image Optimization**: Proper image sizing and lazy loading
4. **Caching Strategy**: Effective caching with local storage or session storage
5. **Bundle Analysis**: Regular analysis and optimization of bundle size

## Accessibility Considerations

1. **Semantic HTML**: Using proper HTML elements for their intended purpose
2. **ARIA Attributes**: Adding ARIA roles and attributes where needed
3. **Keyboard Navigation**: Ensuring all interactive elements are keyboard accessible
4. **Color Contrast**: Maintaining proper contrast ratios
5. **Screen Reader Support**: Testing with screen readers
6. **Focus Management**: Proper focus handling, especially in modals and dialogs

## Deployment Strategy

1. **Environment Configuration**:
   - Development: Local development server with Vite
   - Production: Static file hosting (Netlify, Vercel, or similar)

2. **Build Process**:
   - Manual build using npm scripts
   - Environment variable management for different environments
   - Static file optimization

## Implementation Roadmap

1. **Phase 1: Core Setup**
   - Project setup with Vite and TypeScript
   - Install required dependencies (React Router, Chakra UI, Zustand)
   - Setup basic routing structure
   - Create Chakra UI theme configuration

2. **Phase 2: State Management and Mock Data**
   - Define data types in `api/types.ts`
   - Create Zustand stores with mock data and simulated API operations
   - Add persistence for relevant stores
   - Implement authentication flow with mock user data

3. **Phase 3: Layout Components**
   - Implement main layout structure
   - Create sidebar navigation
   - Build header component
   - Add responsive layout handling

4. **Phase 4: Core Modules - Auth and Dashboard**
   - Implement login functionality
   - Create dashboard with statistics and charts
   - Add user profile management

5. **Phase 5: Card and Rule Management**
   - Implement card management with CRUD operations
   - Build rule template system
   - Create card and template previews

6. **Phase 6: User and Application Management**
   - Implement user management with roles and permissions
   - Create application listing and detailed views
   - Add application approval/rejection workflows

7. **Phase 7: Polish and Refinement**
   - Improve responsive behavior
   - Add loading states and error handling
   - Optimize performance
   - Enhance UI/UX details
{"name": "rionet", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@chakra-ui/react": "^2.10.7", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "date-fns": "^4.1.0", "framer-motion": "^12.9.2", "lodash": "^4.17.21", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "react-icons": "^5.5.0", "react-router-dom": "^7.5.3", "recharts": "^2.15.3", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "msw": "^2.7.5", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}
// // src/types/rule.ts
// export interface Rule {
//     id: string;
//     name: string;
//     description: string;
//     conditions: RuleCondition[];
//     actions: RuleAction[];
//     status: 'active' | 'inactive' | 'draft';
//     createdAt: string;
//     updatedAt: string;
// }

// export interface RuleCondition {
//     id: string;
//     type: 'amount' | 'category' | 'merchant' | 'custom';
//     operator: 'equals' | 'greater_than' | 'less_than' | 'contains';
//     value: string | number;
// }

// export interface RuleAction {
//     id: string;
//     type: 'cashback' | 'points' | 'discount' | 'custom';
//     value: string | number;
//     description: string;
// }

// export interface CreateRuleData {
//     name: string;
//     description: string;
//     conditions: RuleCondition[];
//     actions: RuleAction[];
//     status: 'active' | 'inactive' | 'draft';
// }

// export interface UpdateRuleData {
//     name?: string;
//     description?: string;
//     conditions?: RuleCondition[];
//     actions?: RuleAction[];
//     status?: 'active' | 'inactive' | 'draft';
// }

// export interface RuleState {
//     rules: Rule[];
//     isLoading: boolean;
//     error: Error | null;
//     fetchRules: () => Promise<void>;
//     createRule: (data: CreateRuleData) => Promise<Rule>;
//     updateRule: (id: string, data: UpdateRuleData) => Promise<Rule>;
//     deleteRule: (id: string) => Promise<void>;
// }


// src/types/template.ts
export interface RuleField {
    name: string;
    condition: string;
    value: string;
    action: 'Hard' | 'Soft';
  }
  
  export interface Template {
    id: string;
    name: string;
    templateNumber: string;
    date: string;
    cardLinked: string;
    rules: RuleField[];
  }
  
  export interface TemplateFormData {
    name: string;
    templateNumber: string;
    rules: RuleField[];
  }
  
  // Condition types for the rules
  export const CONDITION_OPTIONS = [
    { value: '=', label: '=' },
    { value: '>', label: '>' },
    { value: '<', label: '<' },
    { value: '>=', label: '>=' },
    { value: '<=', label: '<=' },
  ] as const;
  
  // Rule names that are available
  export const RULE_NAMES = [
    'Age Requirement',
    'Gender',
    'Pincode',
    'Credit Score Requirement',
    'Credit History Length',
  ] as const;
  
  // Credit score options
  export const CREDIT_SCORE_OPTIONS = [
    { value: 'A', label: 'A' },
    { value: 'B', label: 'B' },
    { value: 'C', label: 'C' },
    { value: 'D', label: 'D' },
    { value: 'F', label: 'F' },
  ] as const;
  
  // Gender options
  export const GENDER_OPTIONS = [
    { value: 'M', label: 'M' },
    { value: 'F', label: 'F' },
    { value: 'Other', label: 'Other' },
  ] as const;
  
  // Action types
  export const ACTION_OPTIONS = [
    { value: 'Hard', label: 'Hard' },
    { value: 'Soft', label: 'Soft' },
  ] as const;
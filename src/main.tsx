// src/main.tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON>ider } from '@chakra-ui/react';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import theme from './theme';

// Import routes
import Root from './routes/root';
import Dashboard from './routes/dashboard';
import CardManagement from './routes/card-management/card-management';
import ViewApplications from './routes/view-applications';
import ViewDetails from './routes/view-details';

import ViewUsers from './routes/users/view-users';
import CreateUser from './routes/users/create-user';
import EditUser from './routes/users/edit-user';
import UserDetail from './routes/users/user-detail';

// Card management routes
import EditCard from './routes/card-management/edit-card';

// import CreateCard from './routes/create-card';
// import CreateTemplate from './routes/create-template';
// import EditTemplate from './routes/edit-template';
import Login from './routes/login';
import CreateCard from './routes/card-management/create-card';
import ViewTemplates from './routes/rules/view-templates';
import CreateTemplate from './routes/rules/create-template';
import EditTemplate from './routes/rules/edit-template';

// Create router
const router = createBrowserRouter([
  {
    path: '/',
    element: <Root />,
    children: [
      {
        path: '/',
        element: <Dashboard />,
      },
      {
        path: '/cards',
        element: <CardManagement />,
      },
      // View card route removed as requested
      {
        path: '/cards/edit/:id',
        element: <EditCard />,
      },
      {
        path: '/cards/create',
        element: <CreateCard />,
      },
      {
        path: '/applications',
        element: <ViewApplications />,
      },
      {
        path: '/applications/:id',
        element: <ViewDetails />,
      },
      {
        path: 'users',
        element: <ViewUsers />,
      },
      {
        path: 'users/create',
        element: <CreateUser />,
      },
      {
        path: 'users/:id',
        element: <UserDetail />,
      },
      {
        path: 'users/edit/:id',
        element: <EditUser />,
      },
      {
        path: 'rules',
        element: <ViewTemplates />,
      },
      {
        path: 'rules/create',
        element: <CreateTemplate />,
      },
      {
        path: 'rules/edit/:id',
        element: <EditTemplate />,
      }
    ],
  },
  {
    path: '/login',
    element: <Login />,
  },
]);

// Render app
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ChakraProvider theme={theme}>
      <RouterProvider router={router} />
    </ChakraProvider>
  </React.StrictMode>
);
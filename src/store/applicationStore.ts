import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { useAuthStore } from './authStore';

// Mock application data based on the UI mockups
const mockApplications = [
  {
    id: '1',
    date: '12-10-2024',
    mCode: '<PERSON><PERSON>',
    name: '<PERSON><PERSON>',
    mobile: '1234568971',
    score: 271,
    status: 'Bureau check',
    fullName: '<PERSON><PERSON>',
    employmentType: 'Salaried',
    salaryRange: '10 - 15 LPA',
    pan: '**********',
    creditScore: 720,
    pincode: '560043',
    cardApplied: 'Card Name',
    merchantCode: '511',
    currentStep: 1
  },
  {
    id: '2',
    date: '12-10-2024',
    mCode: '<PERSON><PERSON><PERSON>',
    name: '<PERSON><PERSON><PERSON>',
    mobile: '1234568971',
    score: 271,
    status: 'Card Offered',
    fullName: '<PERSON><PERSON><PERSON>',
    employmentType: 'Salaried',
    salaryRange: '15 - 20 LPA',
    pan: '**********',
    creditScore: 680,
    pincode: '560034',
    cardApplied: 'Premium Card',
    merchantCode: '622',
    currentStep: 2
  },
  {
    id: '3',
    date: '12-10-2024',
    mCode: 'Rahul Kumar',
    name: 'Rahul <PERSON>',
    mobile: '1234568971',
    score: 271,
    status: 'Card Offered',
    fullName: 'Rahul Kumar',
    employmentType: 'Self-Employed',
    salaryRange: '25 - 30 LPA',
    pan: '**********',
    creditScore: 790,
    pincode: '560001',
    cardApplied: 'Gold Card',
    merchantCode: '733',
    currentStep: 2
  },
  {
    id: '4',
    date: '12-10-2024',
    mCode: 'Suraj Vishwas',
    name: 'Suraj Vishwas',
    mobile: '1234568971',
    score: 271,
    status: 'Application Started',
    fullName: 'Suraj Vishwas',
    employmentType: 'Business Owner',
    salaryRange: '35+ LPA',
    pan: '**********',
    creditScore: 820,
    pincode: '560076',
    cardApplied: 'Platinum Card',
    merchantCode: '844',
    currentStep: 0
  },
  {
    id: '5',
    date: '12-10-2024',
    mCode: 'Pratik Kumar',
    name: 'Pratik Kumar',
    mobile: '1234568971',
    score: 271,
    status: 'Card Offered',
    fullName: 'Pratik Kumar',
    employmentType: 'Salaried',
    salaryRange: '20 - 25 LPA',
    pan: '**********',
    creditScore: 710,
    pincode: '560103',
    cardApplied: 'Silver Card',
    merchantCode: '955',
    currentStep: 2
  }
];

// Add more mock data for pagination testing
const generateMockApplications = (count: number) => {
  const names = ['Ashok Kumar', 'Raunil Kumar', 'Rahul Kumar', 'Suraj Vishwas', 'Pratik Kumar', 'Amit Sharma', 'Priya Singh', 'Vikram Patel', 'Sunita Reddy', 'Rajesh Gupta'];
  const statuses = ['Bureau check', 'Card Offered', 'Application Started'];
  const employmentTypes = ['Salaried', 'Self-Employed', 'Business Owner'];
  const cardTypes = ['Card Name', 'Premium Card', 'Gold Card', 'Platinum Card', 'Silver Card'];
  
  return Array.from({ length: count }, (_, index) => ({
    id: (index + 1).toString(),
    date: '12-10-2024',
    mCode: names[index % names.length],
    name: names[index % names.length],
    mobile: `12345689${(index % 100).toString().padStart(2, '0')}`,
    score: 200 + (index % 300),
    status: statuses[index % statuses.length],
    fullName: names[index % names.length],
    employmentType: employmentTypes[index % employmentTypes.length],
    salaryRange: '10 - 15 LPA',
    pan: `ABCDE${(1234 + index).toString()}F`,
    creditScore: 650 + (index % 200),
    pincode: `56004${(index % 10)}`,
    cardApplied: cardTypes[index % cardTypes.length],
    merchantCode: (500 + index).toString(),
    currentStep: index % 3
  }));
};

// Generate 150 mock applications for testing pagination
const allMockApplications = generateMockApplications(150);

export interface FetchApplicationsParams {
  page: number;
  limit: number;
}

export interface ApplicationsResponse {
  applications: typeof mockApplications;
  totalCount: number;
  currentPage: number;
  totalPages: number;
}

// Define the application store type
type ApplicationStore = {
  applications: typeof mockApplications;
  totalCount: number;
  currentPage: number;
  totalPages: number;
  isLoading: boolean;
  error: string | null;
  fetchApplications: (params: FetchApplicationsParams) => Promise<void>;
  getApplicationById: (id?: string) => typeof mockApplications[0] | undefined;
  updateApplicationStatus: (id: string, status: string, step: number) => Promise<void>;
};

// Create the Zustand store with simulated API delays and pagination
export const useApplicationStore = create<ApplicationStore>()(
  persist(
    (set, get) => ({
      applications: [],
      totalCount: 0,
      currentPage: 1,
      totalPages: 0,
      isLoading: false,
      error: null,
      
      // Fetch applications with pagination support
      fetchApplications: async (params: FetchApplicationsParams) => {
        set({ isLoading: true, error: null });
      
        try {
          const token = useAuthStore.getState().token;
      
          // If we have a real API, use it
          if (token && token !== 'mock-jwt-token') {
            const response = await fetch(
              'http://bank-data-pdf-light-mode-*********.ap-south-1.elb.amazonaws.com/card/admin_leads',
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer ${token}`
                },
                body: JSON.stringify({
                  page: params.page,
                  limit: params.limit
                })
              }
            );
      
            if (!response.ok) {
              throw new Error(`API error: ${response.status}`);
            }
      
            const data = await response.json();
      
            // Map the backend response to your frontend format
            const mappedApplications = data.leads.map((lead: any, index: number) => ({
              id: lead.txn_id || ((params.page - 1) * params.limit + index).toString(),
              date: lead.created_at.split(' ')[0],
              mCode: lead.agent_name,
              name: lead.name,
              mobile: lead.phone_no,
              score: 0,
              status: lead.card_status,
              fullName: lead.name,
              employmentType: lead.employment_type,
              salaryRange: lead.salary_range,
              pan: lead.pan_no,
              creditScore: 0,
              pincode: lead.pin_code,
              cardApplied: lead.card_name,
              merchantCode: '',
              currentStep: 0
            }));

            // Derive pagination values - we already know current page from our request
            const totalCount = data.total_count || data.leads.length;
            const totalPages = Math.ceil(totalCount / params.limit);
      
            set({
              applications: mappedApplications,
              totalCount,
              currentPage: params.page,  // We sent this, so we know it
              totalPages,
              isLoading: false
            });
          } else {
            // Simulate API delay for mock data
            await new Promise(resolve => setTimeout(resolve, 800));
            
            // Calculate pagination for mock data
            const startIndex = (params.page - 1) * params.limit;
            const endIndex = startIndex + params.limit;
            const paginatedApplications = allMockApplications.slice(startIndex, endIndex);
            const totalCount = allMockApplications.length;
            const totalPages = Math.ceil(totalCount / params.limit);
            
            set({
              applications: paginatedApplications,
              totalCount,
              currentPage: params.page,
              totalPages,
              isLoading: false
            });
          }
        } catch (error) {
          console.error('Fetch applications error:', error);
          set({
            error: 'Failed to fetch applications. Please try again later.',
            isLoading: false
          });
        }
      },
      
      // Get application by ID (searches in current page + all mock data for details view)
      getApplicationById: (id) => {
        if (!id) return undefined;
        
        // First check current page
        const currentPageApp = get().applications.find(app => app.id === id);
        if (currentPageApp) return currentPageApp;
        
        // Fallback to all mock data for details view
        return allMockApplications.find(app => app.id === id);
      },
      
      // Update application status with simulated API delay
      updateApplicationStatus: async (id, status, step) => {
        set({ isLoading: true, error: null });
        
        try {
          // Simulate API delay
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          set(state => ({
            applications: state.applications.map(app => 
              app.id === id 
                ? { ...app, status, currentStep: step } 
                : app
            ),
            isLoading: false
          }));
        } catch (error) {
          set({ 
            error: 'Failed to update application status. Please try again later.', 
            isLoading: false 
          });
        }
      }
    }),
    {
      name: 'application-store',
      partialize: (state) => ({ 
        // Don't persist pagination state, only applications for caching
        applications: state.applications,
        totalCount: state.totalCount,
        currentPage: state.currentPage,
        totalPages: state.totalPages
      }),
    }
  )
);
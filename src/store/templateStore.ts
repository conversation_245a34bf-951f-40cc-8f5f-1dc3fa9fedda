// src/store/templateStore.ts
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { useAuthStore } from './authStore';

interface RuleField {
  name: string;
  condition: string;
  value: string;
  action: string;
}

interface Template {
  id: string;
  name: string;
  templateNumber: string;
  date: string;
  cardLinked: string;
  rules: RuleField[];
}

interface TemplateState {
  templates: Template[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchTemplates: () => Promise<void>;
  addTemplate: (template: Template) => Promise<void>;
  updateTemplate: (id: string, updatedTemplate: Template) => Promise<void>;
  removeTemplate: (id: string) => Promise<void>;
  getTemplateById: (id: string) => Template | undefined;
}

// Mock data
const mockTemplates: Template[] = [
  {
    id: '1',
    name: 'Standard Eligibility Criteria',
    templateNumber: 'Named - 0001',
    date: '12-10-2024',
    cardLinked: '2',
    rules: [
      { name: 'Age Requirement', condition: '>=', value: '18', action: 'Hard' },
      { name: 'Gender', condition: '=', value: 'M', action: 'Soft' },
      { name: 'Pincode', condition: '=', value: '560001-560068', action: 'Hard' },
      { name: 'Credit Score Requirement', condition: '>=', value: 'B', action: 'Hard' },
      { name: 'Credit History Length', condition: '>=', value: '24', action: 'Soft' },
    ],
  },
  {
    id: '2',
    name: 'Salaried Income Criteria',
    templateNumber: 'Named - 0002',
    date: '12-10-2024',
    cardLinked: '1',
    rules: [
      { name: 'Age Requirement', condition: '>=', value: '21', action: 'Hard' },
      { name: 'Gender', condition: '=', value: 'F', action: 'Soft' },
      { name: 'Pincode', condition: '=', value: '600001-600131', action: 'Hard' },
      { name: 'Credit Score Requirement', condition: '>=', value: 'C', action: 'Hard' },
      { name: 'Credit History Length', condition: '>=', value: '36', action: 'Soft' },
    ],
  },
];

export const useTemplateStore = create<TemplateState>()(
  devtools(
    persist(
      (set, get) => ({
        templates: mockTemplates,
        isLoading: false,
        error: null,

        fetchTemplates: async () => {
          set({ isLoading: true, error: null });
          try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 800));
            
            // Use mock data
            set({ isLoading: false });
          } catch (error) {
            set({ 
              error: error instanceof Error ? error.message : 'Failed to fetch templates',
              isLoading: false 
            });
          }
        },

       addTemplate: async (template: Template) => {
  set({ isLoading: true, error: null });

  try {
    const attributeMap: Record<string, string> = {
      'Age Requirement': 'age',
      'Gender': 'gender',
      'Pincode': 'pincode',
      'Credit Score Requirement': 'credit_score',
      'Credit History Length': 'credit_history',
    };

    const formattedRules = template.rules.map(rule => ({
      name: rule.name,
      attribute: attributeMap[rule.name] || rule.name.toLowerCase().replace(/\s+/g, '_'),
      operator: rule.condition === '=' ? '==' : rule.condition,
      value: rule.name === 'Gender' ? `['${rule.value}']` : rule.value,
      rule_type: rule.action,
      ...(rule.action === 'Soft' ? { weight: 5 } : {}),
    }));

    const payload = {
      rule_set_id: parseInt(template.templateNumber) || 1, // fallback to 1 if parsing fails
      rules: formattedRules,
    };

    const token = useAuthStore.getState().token;

    const response = await fetch(
      'http://bank-data-pdf-light-mode-*********.ap-south-1.elb.amazonaws.com/card/ruleset-create',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(payload),
      }
    );

    if (!response.ok) {
      const err = await response.text();
      throw new Error(err || 'Failed to create template');
    }

    set(state => ({
      templates: [...state.templates, template],
      isLoading: false
    }));
  } catch (error) {
    set({
      error: error instanceof Error ? error.message : 'Failed to add template',
      isLoading: false,
    });
    throw error;
  }
},

        updateTemplate: async (id: string, updatedTemplate: Template) => {
          set({ isLoading: true, error: null });
          try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 500));
            
            set(state => ({
              templates: state.templates.map(template =>
                template.id === id ? updatedTemplate : template
              ),
              isLoading: false
            }));
          } catch (error) {
            set({ 
              error: error instanceof Error ? error.message : 'Failed to update template',
              isLoading: false 
            });
            throw error;
          }
        },

        removeTemplate: async (id: string) => {
          set({ isLoading: true, error: null });
          try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 500));
            
            set(state => ({
              templates: state.templates.filter(template => template.id !== id),
              isLoading: false
            }));
          } catch (error) {
            set({ 
              error: error instanceof Error ? error.message : 'Failed to remove template',
              isLoading: false 
            });
            throw error;
          }
        },

        getTemplateById: (id: string) => {
          const state = get();
          return state.templates.find(template => template.id === id);
        },
      }),
      {
        name: 'template-storage',
        partialize: (state) => ({ templates: state.templates })
      }
    ),
    { name: 'TemplateStore' }
  )
);
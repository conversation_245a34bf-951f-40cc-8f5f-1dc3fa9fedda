// src/store/ruleStore.ts
import { create } from 'zustand';
import { Rule, RuleState, CreateRuleData, UpdateRuleData } from '../types/rule';

// Mock data
const mockRules: Rule[] = [
  {
    id: 'rule-1',
    name: 'Basic Cashback Rule',
    description: 'Standard cashback for all purchases',
    conditions: [
      {
        id: 'cond-1',
        type: 'amount',
        operator: 'greater_than',
        value: 100
      }
    ],
    actions: [
      {
        id: 'action-1',
        type: 'cashback',
        value: 2,
        description: '2% cashback on purchases above ₹100'
      }
    ],
    status: 'active',
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-01T00:00:00Z'
  },
  {
    id: 'rule-2',
    name: 'Premium Cashback Rule',
    description: 'Enhanced cashback for premium cardholders',
    conditions: [
      {
        id: 'cond-2',
        type: 'amount',
        operator: 'greater_than',
        value: 500
      }
    ],
    actions: [
      {
        id: 'action-2',
        type: 'cashback',
        value: 5,
        description: '5% cashback on purchases above ₹500'
      }
    ],
    status: 'active',
    createdAt: '2025-01-02T00:00:00Z',
    updatedAt: '2025-01-02T00:00:00Z'
  },
  {
    id: 'rule-3',
    name: 'Lounge Access Rule',
    description: 'Free lounge access benefit',
    conditions: [
      {
        id: 'cond-3',
        type: 'custom',
        operator: 'equals',
        value: 'premium_card'
      }
    ],
    actions: [
      {
        id: 'action-3',
        type: 'custom',
        value: 4,
        description: '4 free domestic lounge visits per year'
      }
    ],
    status: 'active',
    createdAt: '2025-01-03T00:00:00Z',
    updatedAt: '2025-01-03T00:00:00Z'
  }
];

export const useRuleStore = create<RuleState>((set, get) => ({
  rules: mockRules,
  isLoading: false,
  error: null,

  fetchRules: async () => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      // Rules are already loaded from mock data
      set({ isLoading: false });
    } catch (error) {
      set({ error: error as Error, isLoading: false });
    }
  },

  createRule: async (data: CreateRuleData) => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const newRule: Rule = {
        ...data,
        id: `rule-${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      set((state) => ({
        rules: [...state.rules, newRule],
        isLoading: false
      }));

      return newRule;
    } catch (error) {
      set({ error: error as Error, isLoading: false });
      throw error;
    }
  },

  updateRule: async (id: string, data: UpdateRuleData) => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));
      
      set((state) => {
        const updatedRules = state.rules.map((rule) => {
          if (rule.id === id) {
            return {
              ...rule,
              ...data,
              updatedAt: new Date().toISOString()
            };
          }
          return rule;
        });

        const updatedRule = updatedRules.find(rule => rule.id === id);
        if (!updatedRule) {
          throw new Error('Rule not found');
        }

        return { rules: updatedRules, isLoading: false };
      });

      const state = get();
      const updatedRule = state.rules.find(rule => rule.id === id);
      if (!updatedRule) {
        throw new Error('Rule not found');
      }

      return updatedRule;
    } catch (error) {
      set({ error: error as Error, isLoading: false });
      throw error;
    }
  },

  deleteRule: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 600));
      
      set((state) => ({
        rules: state.rules.filter((rule) => rule.id !== id),
        isLoading: false
      }));
    } catch (error) {
      set({ error: error as Error, isLoading: false });
      throw error;
    }
  }
}));
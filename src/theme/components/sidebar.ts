// src/theme/components/sidebar.ts
import { defineStyleConfig } from '@chakra-ui/react';

const Sidebar = defineStyleConfig({
  baseStyle: {
    container: {
      bg: 'white', // White background as per mockup
      borderRight: '1px',
      borderColor: 'gray.200',
      width: '260px',
      height: '100vh',
      position: 'sticky',
      top: 0,
      zIndex: 10,
      transition: 'all 0.3s ease',
    },
    link: {
      display: 'flex',
      alignItems: 'center',
      py: 2.5,
      px: 4,
      borderRadius: 'md',
      fontSize: 'sm',
      fontWeight: 'medium',
      color: 'gray.600', // Dark gray text
      transition: 'all 0.2s ease',
      _hover: {
        bg: 'gray.50', // Light gray background on hover
        color: 'brand.500', // Primary blue text on hover
      },
      _active: {
        bg: 'brand.50', // Very light blue when active
        color: 'brand.500', // Primary blue text when active
      },
    },
    linkActive: {
      bg: 'brand.50', // Very light blue for active link
      color: 'brand.500', // Primary blue text for active link
      fontWeight: 'semibold',
    },
    icon: {
      mr: 3,
      fontSize: '1.2em',
      color: 'gray.500', // Gray icons
    },
    divider: {
      my: 4,
      borderColor: 'gray.200',
    },
    header: {
      p: 4,
      textAlign: 'center',
      bg: 'white', // White header
      borderBottom: '1px',
      borderColor: 'gray.200',
    },
    headerText: {
      fontSize: 'xl',
      fontWeight: 'bold',
      color: 'gray.800',
    },
  },
});

export default Sidebar;

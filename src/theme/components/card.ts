// src/theme/components/card.ts
import { defineStyleConfig } from '@chakra-ui/react';

const Card = defineStyleConfig({
  baseStyle: {
    container: {
      bg: 'white',
      borderRadius: 'xl',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)', // Subtle shadow instead of border
      overflow: 'hidden',
      transition: 'all 0.3s ease-in-out',
      _hover: {
        transform: 'translateY(-2px)',
        boxShadow: '0 6px 16px rgba(0, 0, 0, 0.07)',
      },
    },
    header: {
      p: 5,
      borderBottom: 'none',
      position: 'relative',
      _after: {
        content: '""',
        position: 'absolute',
        bottom: 0,
        left: '5%',
        width: '90%',
        height: '1px',
        bg: 'gray.100',
      },
      bg: 'white', // White header
    },
    body: {
      p: 5,
    },
    footer: {
      p: 5,
      borderTop: 'none',
      position: 'relative',
      _before: {
        content: '""',
        position: 'absolute',
        top: 0,
        left: '5%',
        width: '90%',
        height: '1px',
        bg: 'gray.100',
      },
      bg: 'white', // White footer
    },
  },
  variants: {
    elevated: {
      container: {
        boxShadow: '0 8px 16px rgba(0, 0, 0, 0.08)',
      },
    },
    outline: {
      container: {
        boxShadow: 'none',
        border: 'none', // No border as per user preference
        _hover: {
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
        },
      },
    },
    shadow: {
      container: {
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
      },
    },
    primary: {
      container: {
        boxShadow: '0 4px 12px rgba(25, 74, 156, 0.1)', // Brand color shadow
        border: 'none', // No border as per user preference
      },
      header: {
        bg: 'brand.500', // Primary blue header
        color: 'white',
        borderBottom: 'none',
        position: 'relative',
        _after: {
          content: '""',
          position: 'absolute',
          bottom: 0,
          left: '5%',
          width: '90%',
          height: '1px',
          bg: 'rgba(255, 255, 255, 0.2)',
        },
      },
    },
    secondary: {
      container: {
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
        border: 'none', // No border as per user preference
      },
      header: {
        bg: 'gray.400', // Gray header
        color: 'white',
        borderBottom: 'none',
        position: 'relative',
        _after: {
          content: '""',
          position: 'absolute',
          bottom: 0,
          left: '5%',
          width: '90%',
          height: '1px',
          bg: 'rgba(255, 255, 255, 0.2)',
        },
      },
    },
    navy: {
      container: {
        boxShadow: '0 4px 12px rgba(52, 60, 106, 0.1)', // Navy color shadow
        border: 'none', // No border as per user preference
      },
      header: {
        bg: 'brand.300', // Navy blue header
        color: 'white',
        borderBottom: 'none',
        position: 'relative',
        _after: {
          content: '""',
          position: 'absolute',
          bottom: 0,
          left: '5%',
          width: '90%',
          height: '1px',
          bg: 'rgba(255, 255, 255, 0.2)',
        },
      },
    },
  },
  defaultProps: {
    variant: 'shadow',
  },
});

export default Card;

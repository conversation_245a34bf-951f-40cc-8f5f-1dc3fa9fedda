// src/theme/components/button.ts
import { defineStyleConfig } from '@chakra-ui/react';

const Button = defineStyleConfig({
  baseStyle: {
    fontWeight: 'medium',
    borderRadius: 'md',
    _focus: {
      boxShadow: 'outline',
    },
  },
  sizes: {
    sm: {
      fontSize: 'sm',
      px: 4,
      py: 2,
    },
    md: {
      fontSize: 'md',
      px: 6,
      py: 2.5,
    },
    lg: {
      fontSize: 'md',
      px: 8,
      py: 3,
    },
  },
  variants: {
    solid: {
      bg: 'brand.500', // Primary blue #194A9C
      color: 'white',
      _hover: {
        bg: 'brand.600', // Darker blue #123288
        _disabled: {
          bg: 'brand.500',
        },
      },
      _active: { bg: 'brand.700' }, // Even darker blue
    },
    secondary: {
      bg: 'gray.400', // #A6A6A6
      color: 'white',
      _hover: {
        bg: 'gray.500', // #898989
        _disabled: {
          bg: 'gray.400',
        },
      },
      _active: { bg: 'gray.600' },
    },
    outline: {
      border: '1px solid',
      borderColor: 'brand.500', // Primary blue
      color: 'brand.500',
      _hover: {
        bg: 'brand.50', // Very light blue
      },
      _active: {
        bg: 'brand.100', // Light gray/blue
      },
    },
    ghost: {
      color: 'gray.600',
      _hover: {
        bg: 'gray.100',
      },
      _active: {
        bg: 'gray.200',
      },
    },
    navy: {
      bg: 'brand.300', // Navy blue #343C6A
      color: 'white',
      _hover: {
        bg: 'brand.400', // Darker blue
        _disabled: {
          bg: 'brand.300',
        },
      },
      _active: { bg: 'brand.400' },
    },
    lightBlue: {
      bg: 'brand.200', // Light blue #718EBF
      color: 'white',
      _hover: {
        bg: 'brand.300',
        _disabled: {
          bg: 'brand.200',
        },
      },
      _active: { bg: 'brand.300' },
    },
  },
  defaultProps: {
    size: 'md',
    variant: 'solid',
  },
});

export default Button;

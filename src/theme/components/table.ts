// src/theme/components/table.ts
import { tableAnatomy } from '@chakra-ui/anatomy';
import { createMultiStyleConfigHelpers } from '@chakra-ui/react';

const { definePartsStyle, defineMultiStyleConfig } = createMultiStyleConfigHelpers(tableAnatomy.keys);

const baseStyle = definePartsStyle({
  table: {
    fontVariantNumeric: 'lining-nums tabular-nums',
    borderCollapse: 'separate',
    borderSpacing: '0',
    width: 'full',
    overflow: 'hidden',
    borderRadius: 'lg',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
  },
  th: {
    fontFamily: 'heading',
    fontWeight: 'normal', // Changed from 'semibold' to match your component
    textTransform: 'none',
    letterSpacing: 'normal',
    textAlign: 'center', // Changed from 'start' to 'center'
    bg: 'white', // Changed from 'brand.500' to white
    // color: 'blue.500', // Changed from 'white' to blue.500
    color: '#718EBF',
    borderBottom: '1px solid',
    borderColor: 'gray.100',
    px: 4,
    py: 4,
    fontSize: 'md', // Changed from 'sm' to 'md'
    position: 'relative',
    _first: {
      borderTopLeftRadius: 'lg',
    },
    _last: {
      borderTopRightRadius: 'lg',
    },
  },
  td: {
    px: 4,
    py: 4,
    borderBottom: '1px',
    borderColor: 'gray.100',
    fontSize: 'sm',
    textAlign: 'center', // Added center alignment
    transition: 'all 0.2s',
  },
  caption: {
    mt: 4,
    fontFamily: 'heading',
    textAlign: 'center',
    fontWeight: 'medium',
    color: 'gray.500',
  },
  tbody: {
    bg: 'white',
    tr: {
      transition: 'all 0.2s',
      _hover: {
        bg: 'gray.50', // Changed from 'brand.50' to match your hoverBg
        transform: 'translateY(-1px)',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
      },
    },
  },
});

// Updated simple variant to match your component styling
const variantSimple = definePartsStyle({
  table: {
    borderRadius: 'lg',
    overflow: 'hidden',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
  },
  th: {
    // color: 'blue.500', // Changed to blue text
    color: '#718EBF',
    bg: 'white', // Changed to white background
    borderBottom: '1px solid',
    borderColor: 'gray.100',
    fontWeight: 'normal',
    textAlign: 'center',
    fontSize: 'md',
    py: 4,
    position: 'relative',
    _first: {
      borderTopLeftRadius: 'lg',
    },
    _last: {
      borderTopRightRadius: 'lg',
    },
  },
  td: {
    borderBottom: '1px',
    borderColor: 'gray.100',
    textAlign: 'center',
  },
  tbody: {
    bg: 'white',
    tr: {
      transition: 'all 0.2s',
      _hover: {
        bg: 'gray.50', // Light gray hover instead of brand color
        transform: 'translateY(-1px)',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
      },
      _last: {
        td: {
          borderBottom: 'none',
        },
      },
    },
  },
});

// Keep the navy variant for alternative styling
const variantNavy = definePartsStyle({
  table: {
    borderRadius: 'lg',
    overflow: 'hidden',
    boxShadow: '0 2px 8px rgba(52, 60, 106, 0.08)',
  },
  th: {
    color: 'white',
    bg: 'brand.300', // Navy blue #343C6A
    borderBottom: 'none',
    position: 'relative',
    _first: {
      borderTopLeftRadius: 'lg',
    },
    _last: {
      borderTopRightRadius: 'lg',
    },
  },
  td: {
    borderBottom: '1px',
    borderColor: 'gray.100',
    textAlign: 'center', // Added center alignment
  },
  tbody: {
    bg: 'white',
    tr: {
      transition: 'all 0.2s',
      _hover: {
        bg: 'brand.50',
        transform: 'translateY(-1px)',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
      },
      _last: {
        td: {
          borderBottom: 'none',
        },
      },
    },
  },
});

// Add a new unstyled variant that matches your component's variant="unstyled"
const variantUnstyled = definePartsStyle({
  table: {
    borderRadius: 'lg',
    overflow: 'hidden',
  },
  th: {
    // color: 'blue.500',
    color: '#718EBF',
    bg: 'white',
    fontWeight: 'normal',
    textAlign: 'center',
    fontSize: 'md',
    py: 4,
    px: 4, // Added px for consistency
    borderBottom: '1px solid',
    borderColor: 'gray.100',
    _first: {
      borderTopLeftRadius: 'lg',
    },
    _last: {
      borderTopRightRadius: 'lg',
    },
  },
  td: {
    borderBottom: '1px',
    borderColor: 'gray.100',
    textAlign: 'center',
    py: 4,
    px: 4,
  },
  tbody: {
    bg: 'white',
    tr: {
      transition: 'all 0.2s',
      td: {
        color: 'gray.800',
      },
      _hover: {
        bg: { base: 'gray.50', _dark: 'gray.700' }, // Using responsive values like your hoverBg
      },
      _last: {
        td: {
          borderBottom: 'none',
        },
      },
    },
  },
});

const Table = defineMultiStyleConfig({
  baseStyle,
  variants: {
    simple: variantSimple,
    navy: variantNavy,
    unstyled: variantUnstyled, // Added the unstyled variant
  },
  defaultProps: {
    variant: 'simple',
  },
});

export default Table;

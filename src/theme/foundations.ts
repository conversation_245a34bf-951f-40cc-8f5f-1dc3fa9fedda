// src/theme/foundations.ts
export default {
    colors: {
      brand: {
        50: '#E6EFF5', // Very light blue
        100: '#F5F7FA', // Very light gray/blue
        200: '#718EBF', // Light blue
        300: '#343C6A', // Navy blue
        400: '#123288', // Dark blue
        500: '#194A9C', // Primary blue (Primary 2)
        600: '#123288', // Darker blue
        700: '#0D2166', // Even darker blue
        800: '#091644', // Very dark blue
        900: '#050B22', // Almost black blue
      },
      gray: {
        50: '#F2F4F7',  // Off-white
        100: '#E6E6E6', // Very light gray
        200: '#CCCCCC', // Light gray
        300: '#B3B3B3', // Medium light gray
        400: '#A6A6A6', // Medium gray
        500: '#898989', // Medium dark gray
        600: '#666666', // Dark gray
        700: '#4D4D4D', // Very dark gray
        800: '#232323', // Almost black
        900: '#121212', // Black
      },
    },
    fonts: {
      heading: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif',
      body: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif',
    },
    fontSizes: {
      xs: '0.75rem',
      sm: '0.875rem',
      md: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
      '6xl': '3.75rem',
      '7xl': '4.5rem',
      '8xl': '6rem',
      '9xl': '8rem',
    },
    breakpoints: {
      sm: '30em',
      md: '48em',
      lg: '62em',
      xl: '80em',
      '2xl': '96em',
    },
  };
// src/theme/index.ts
import { extendTheme } from "@chakra-ui/react"
import foundations from './foundations';

// Component style overrides
import Button from './components/button';
import Card from './components/card';
import Sidebar from './components/sidebar';
import Table from './components/table';

const overrides = {
  ...foundations,
  components: {
    Button,
    Card,
    Sidebar,
    Table,
  },
  styles: {
    global: {
      body: {
        bg: 'gray.50', // Using our new light gray background
        color: 'gray.800',
      }
    }
  }
};

export default extendTheme(overrides);
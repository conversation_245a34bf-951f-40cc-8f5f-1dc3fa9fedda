import { useEffect } from 'react';
import {
  Box,
  Grid,
  GridItem,
  Heading,
  Text,
  Flex,
  Icon,
  Select,
} from '@chakra-ui/react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Bar<PERSON>hart,
  Bar,
  Legend,
} from 'recharts';
import { FiFileText, FiCheckCircle } from 'react-icons/fi';

// Store
import { useDashboardStore } from '../store/dashboardStore';

// Components
import StatsCard from '../components/dashboard/StatsCard';
import CardMetricsChart from '../components/dashboard/CardMetricsChart';

const Dashboard = () => {
  const {
    isLoading,
    error,
    totalApplications,
    totalApproved,
    applicationTrend,
    approvalTrend,
    applicationMetrics,
    cardMetrics,
    fetchDashboardData,
    timeframe,
    setTimeframe
  } = useDashboardStore();

  // Fetch dashboard data on mount
  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Handle time period change
  const handleTimeframeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setTimeframe(e.target.value as 'daily' | 'weekly' | 'monthly' | '3months');
  };

  if (error) {
    return (
      <Box textAlign="center" py={10} px={6}>
        <Heading as="h2" size="xl" color="red.500">
          Error Loading Dashboard
        </Heading>
        <Text mt={4}>{error}</Text>
      </Box>
    );
  }

  return (
    <Box>

      {/* Main Layout Grid - Stats Cards and Card Metrics Chart side by side */}
      <Grid templateColumns={{ base: "1fr", md: "250px 1fr" }} gap={6} mb={6}>
        {/* Left Column - Vertically Stacked Stats Cards */}
        <GridItem>
          <Flex direction="column" gap={6}>
            {/* Total Applications Card */}
            <StatsCard
              title="Total Applications"
              stat={totalApplications.toString()}
              icon={<Icon as={FiFileText} w={10} h={10} />}
              trend={applicationTrend}
              trendColor="blue"
              isLoading={isLoading}
            />

            {/* Total Approved Card */}
            <StatsCard
              title="Total Approved"
              stat={totalApproved.toString()}
              icon={<Icon as={FiCheckCircle} w={10} h={10} />}
              trend={approvalTrend}
              trendColor="green"
              isLoading={isLoading}
            />
          </Flex>
        </GridItem>

        {/* Right Column - Card Metrics Chart */}
        <GridItem>
          <Box
            bg="white"
            borderRadius="lg"
            boxShadow="sm"
            p={6}
            h="100%"
          >
            <Heading as="h2" size="md" mb={4}>
              Card Metric
            </Heading>

            <CardMetricsChart data={cardMetrics} isLoading={isLoading} />
          </Box>
        </GridItem>
      </Grid>

      {/* Application Metrics Chart - Full Width Below */}
      <Box
        bg="white"
        borderRadius="lg"
        boxShadow="sm"
        p={6}
      >
        <Flex justify="space-between" align="center" mb={4}>
          <Heading as="h2" size="md">
            Total Application Metrics
          </Heading>

          <Select
            value={timeframe}
            onChange={handleTimeframeChange}
            w="140px"
            size="sm"
          >
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="3months">Past 3 Months</option>
          </Select>
        </Flex>

        {isLoading ? (
          <Flex justify="center" align="center" h="300px">
            <Text>Loading chart data...</Text>
          </Flex>
        ) : (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={applicationMetrics}>
              <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
              <XAxis dataKey="name" axisLine={false} tickLine={false} />
              <YAxis axisLine={false} tickLine={false} />
              <Tooltip />
              <Legend
                align="right"
                verticalAlign="top"
                iconType="circle"
                iconSize={8}
              />
              <Bar dataKey="application" name="Application" fill="#3182CE" barSize={30} />
              <Bar dataKey="approved" name="Approved" fill="#4FD1C5" barSize={30} />
            </BarChart>
          </ResponsiveContainer>
        )}
      </Box>
    </Box>
  );
};

export default Dashboard;
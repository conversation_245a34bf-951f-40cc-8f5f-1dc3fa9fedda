// src/routes/rules/create-template.tsx
import React, { useState } from 'react';
import {
  Box,
  Heading,
  FormControl,
  FormLabel,
  Input,
  Select,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Flex,
  useToast,
  IconButton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Grid,
  GridItem,
} from '@chakra-ui/react';
// import { FiDownload, FiUpload } from '@chakra-ui/icons';
import { FiDownload, FiUpload } from "react-icons/fi";
import { useNavigate } from 'react-router-dom';
import { useTemplateStore } from '../../store/templateStore';
import { useForm } from 'react-hook-form';

interface RuleField {
  name: string;
  condition: string;
  value: string;
  action: string;
}

interface TemplateForm {
  name: string;
  templateNumber: string;
  rules: RuleField[];
}

const CreateTemplate = () => {
  const navigate = useNavigate();
  const { addTemplate } = useTemplateStore();
  const toast = useToast();
  const { register, handleSubmit, formState: { errors } } = useForm<TemplateForm>();

  const [rules, setRules] = useState<RuleField[]>([
    { name: 'Age Requirement', condition: '=', value: '', action: 'Hard' },
    { name: 'Gender', condition: '=', value: 'M', action: 'Soft' },
    { name: 'Pincode', condition: '=', value: '', action: 'Hard' },
    { name: 'Credit Score Requirement', condition: '=', value: 'F', action: 'Hard' },
    { name: 'Credit History Length', condition: '=', value: '', action: 'Soft' },
  ]);

  const handleRuleChange = (index: number, field: keyof RuleField, value: string) => {
    const newRules = [...rules];
    newRules[index] = { ...newRules[index], [field]: value };
    setRules(newRules);
  };

  const onSubmit = async (data: TemplateForm) => {
    try {
      const template = {
        id: Date.now().toString(),
        name: data.name,
        templateNumber: data.templateNumber,
        date: new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        }),
        cardLinked: '0', // Default value
        rules: rules,
      };

      await addTemplate(template);

      toast({
        title: 'Template created successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      navigate('/rules');
    } catch (error) {
      toast({
        title: 'Error creating template',
        description: 'Failed to create template. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  return (
    <Box>

      <form onSubmit={handleSubmit(onSubmit)}>
        <Box mb={6}>
          <FormControl mb={4} isInvalid={!!errors.templateNumber}>
            <FormLabel>Template Number</FormLabel>
            <Input
              placeholder="Enter Template Name"
              {...register('templateNumber', { required: 'Template number is required' })}
            />
          </FormControl>
        </Box>

        <Table variant="unstyled" size="md" mb={6}>
          <Thead>
            <Tr>
              <Th>Rule Name</Th>
              <Th>Condition</Th>
              <Th>Value</Th>
              <Th>Action</Th>
            </Tr>
          </Thead>
          <Tbody>
            {rules.map((rule, index) => (
              <Tr key={index}>
                <Td>{rule.name}</Td>
                <Td>
                  <Select
                    value={rule.condition}
                    onChange={(e) => handleRuleChange(index, 'condition', e.target.value)}
                  >
                    <option value="=">=</option>
                    <option value=">">&gt;</option>
                    <option value="<">&lt;</option>
                    <option value=">=">&gt;=</option>
                    <option value="<=">&lt;=</option>
                  </Select>
                </Td>
                <Td>
                  {rule.name === 'Gender' ? (
                    <Select
                      value={rule.value}
                      onChange={(e) => handleRuleChange(index, 'value', e.target.value)}
                    >
                      <option value="M">M</option>
                      <option value="F">F</option>
                      <option value="Other">Other</option>
                    </Select>
                  ) : rule.name === 'Credit Score Requirement' ? (
                    <Select
                      value={rule.value}
                      onChange={(e) => handleRuleChange(index, 'value', e.target.value)}
                    >
                      <option value="A">A</option>
                      <option value="B">B</option>
                      <option value="C">C</option>
                      <option value="D">D</option>
                      <option value="F">F</option>
                    </Select>
                  ) : rule.name === 'Pincode' ? (
                    <Button
                      leftIcon={<FiUpload />}
                      variant="outline"
                      size="sm"
                      onClick={() => console.log('Upload pincode file')}
                    >
                      Upload
                    </Button>
                  ) : (
                    <Input
                      placeholder="Enter Value"
                      value={rule.value}
                      onChange={(e) => handleRuleChange(index, 'value', e.target.value)}
                    />
                  )}
                </Td>
                <Td>
                  <Select
                    value={rule.action}
                    onChange={(e) => handleRuleChange(index, 'action', e.target.value)}
                  >
                    <option value="Hard">Hard</option>
                    <option value="Soft">Soft</option>
                  </Select>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>

        <Flex justifyContent="flex-end">
          <Button
            colorScheme="blue"
            type="submit"
            size="lg"
            width="120px"
          >
            Create
          </Button>
        </Flex>
      </form>
    </Box>
  );
};

export default CreateTemplate;
// src/routes/users/user-detail.tsx
import { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Heading,
  Text,
  SimpleGrid,
  Spinner,
  Center,
  Badge,
  Flex,
  Table,
  Tbody,
  Tr,
  Td,
  TableContainer,
  HStack,
  useToast,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  useDisclosure
} from '@chakra-ui/react';
import { useRef, useState } from 'react';
import { useUserStore } from '../../store/userStore';
import { useAuthStore } from '../../store/authStore';

const UserDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const cancelRef = useRef<HTMLButtonElement>(null);

  const { user } = useAuthStore();

const canEditUser = user?.permissions?.find(p => p.module_name === 'users')?.can_edit;
  
  const { 
    currentUser, 
    isLoading, 
    error, 
    fetchUserById,
    deleteUser
  } = useUserStore();
  
  useEffect(() => {
    if (id) {
      fetchUserById(id);
    }
  }, [id, fetchUserById]);
  
  useEffect(() => {
    if (error) {
      toast({
        title: 'Error',
        description: error,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  }, [error, toast]);
  
  const handleEdit = () => {
    navigate(`/users/edit/${id}`);
  };
  
  const handleDelete = async () => {
    if (!id) return;
    
    try {
      await deleteUser(id);
      toast({
        title: 'User deleted',
        description: 'User has been successfully deleted.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      navigate('/users');
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete user. Please try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      onClose();
    }
  };
  
  // Show loading state
  if (isLoading && !currentUser) {
    return (
      <Center h="200px">
        <Spinner size="xl" />
      </Center>
    );
  }
  
  // Show error state if no user found
  if (!isLoading && !currentUser && !error) {
    return (
      <Center h="200px">
        <Box textAlign="center">
          <Heading size="md">User not found</Heading>
          <Button mt={4} onClick={() => navigate('/users')}>
            Back to Users
          </Button>
        </Box>
      </Center>
    );
  }
  
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return <Badge colorScheme="green">Approved</Badge>;
      case 'pending':
        return <Badge colorScheme="red">Pending</Badge>;
      default:
        return <Badge colorScheme="gray">{status}</Badge>;
    }
  };
  
  return (
    <Box>
      <Flex justifyContent="space-between" alignItems="center" mb={6}>
        <Heading size="lg">User Details</Heading>
        <HStack>
        {canEditUser && (
          <Button colorScheme="red" variant="outline" onClick={onOpen}>
            Delete
          </Button>
          )}
           {canEditUser && (
          <Button colorScheme="blue" onClick={handleEdit}>
            Edit
          </Button>
        )}
        </HStack>
      </Flex>
      
      {currentUser && (
        <Box>
          {/* User Information */}
          <Box 
            bg="white" 
            p={6} 
            borderRadius="md" 
            boxShadow="sm"
            mb={6}
          >
            <Heading size="md" mb={4}>Basic Information</Heading>
            
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
              <Box>
                <Text fontWeight="bold">Employee Code</Text>
                <Text>{currentUser.employeeCode}</Text>
              </Box>
              
              <Box>
                <Text fontWeight="bold">Full Name</Text>
                <Text>{currentUser.fullName}</Text>
              </Box>
              
              <Box>
                <Text fontWeight="bold">Mobile</Text>
                <Text>{currentUser.mobile}</Text>
              </Box>
              
              <Box>
                <Text fontWeight="bold">Status</Text>
                <Text>{getStatusBadge(currentUser.status)}</Text>
              </Box>
            </SimpleGrid>
          </Box>
          
          {/* Roles and Permissions */}
          <Box 
            bg="white" 
            p={6} 
            borderRadius="md" 
            boxShadow="sm"
          >
            <Heading size="md" mb={4}>Roles and Permissions</Heading>
            
            <TableContainer>
              <Table variant="unstyled">
                <Tbody>
                  <Tr>
                    <Td fontWeight="bold" width="150px">Dashboard</Td>
                    <Td>
                      {currentUser.roles.dashboard.view && (
                        <Badge mr={2} colorScheme="blue">View</Badge>
                      )}
                      {currentUser.roles.dashboard.edit && (
                        <Badge colorScheme="green">Edit</Badge>
                      )}
                    </Td>
                  </Tr>
                  
                  <Tr>
                    <Td fontWeight="bold">Users</Td>
                    <Td>
                      {currentUser.roles.users.view && (
                        <Badge mr={2} colorScheme="blue">View</Badge>
                      )}
                      {currentUser.roles.users.edit && (
                        <Badge colorScheme="green">Edit</Badge>
                      )}
                    </Td>
                  </Tr>
                  
                  <Tr>
                    <Td fontWeight="bold">Cards</Td>
                    <Td>
                      {currentUser.roles.cards.view && (
                        <Badge mr={2} colorScheme="blue">View</Badge>
                      )}
                      {currentUser.roles.cards.edit && (
                        <Badge colorScheme="green">Edit</Badge>
                      )}
                    </Td>
                  </Tr>
                  
                  <Tr>
                    <Td fontWeight="bold">Rule</Td>
                    <Td>
                      {currentUser.roles.rule.view && (
                        <Badge mr={2} colorScheme="blue">View</Badge>
                      )}
                      {currentUser.roles.rule.edit && (
                        <Badge colorScheme="green">Edit</Badge>
                      )}
                    </Td>
                  </Tr>
                  
                  <Tr>
                    <Td fontWeight="bold">Applications</Td>
                    <Td>
                      {currentUser.roles.applications.view && (
                        <Badge mr={2} colorScheme="blue">View</Badge>
                      )}
                      {currentUser.roles.applications.edit && (
                        <Badge colorScheme="green">Edit</Badge>
                      )}
                    </Td>
                  </Tr>
                </Tbody>
              </Table>
            </TableContainer>
          </Box>
        </Box>
      )}
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog
        isOpen={isOpen}
        leastDestructiveRef={cancelRef}
        onClose={onClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              Delete User
            </AlertDialogHeader>

            <AlertDialogBody>
              Are you sure you want to delete this user? This action cannot be undone.
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onClose}>
                Cancel
              </Button>
              <Button colorScheme="red" onClick={handleDelete} ml={3}>
                Delete
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Box>
  );
};

export default UserDetail;
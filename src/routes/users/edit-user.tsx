// src/routes/users/edit-user.tsx
import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormLabel,
  Heading,
  Input,
  SimpleGrid,
  VStack,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  useToast,
  Spinner,
  Center,
  Select,
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { useUserStore } from '../../store/userStore';

const EditUser = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const toast = useToast();

  const {
    currentUser,
    isLoading,
    error,
    fetchUserById,
    updateUser
  } = useUserStore();

  const { register, handleSubmit, formState: { errors }, reset } = useForm();

  // State for role permissions
  const [roles, setRoles] = useState({
    dashboard: { view: false, edit: false },
    users: { view: false, edit: false },
    cards: { view: false, edit: false },
    rule: { view: false, edit: false },
    applications: { view: false, edit: false },
  });

  // Fetch user data when component mounts
  useEffect(() => {
    if (id) {
      fetchUserById(id);
    }
  }, [id, fetchUserById]);

  // Set form values when user data is loaded
  useEffect(() => {
    if (currentUser) {
      reset({
        employeeCode: currentUser.employeeCode,
        fullName: currentUser.fullName,
        mobile: currentUser.mobile,
        status: currentUser.status,
      });

      setRoles(currentUser.roles);
    }
  }, [currentUser, reset]);

  // Handle errors
  useEffect(() => {
    if (error) {
      toast({
        title: 'Error',
        description: error,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  }, [error, toast]);

  const handleRoleChange = (module: string, permission: string, checked: boolean) => {
    setRoles(prev => ({
      ...prev,
      [module]: {
        ...prev[module],
        [permission]: checked
      }
    }));
  };

  const onSubmit = async (data: any) => {
    if (!id || !currentUser) return;

    try {
      await updateUser(id, {
        employeeCode: data.employeeCode,
        fullName: data.fullName,
        name: data.fullName, // Update name with fullName for consistency
        mobile: data.mobile,
        status: data.status,
        roles: roles,
      });

      toast({
        title: 'User updated',
        description: 'User has been successfully updated.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      navigate('/users');
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update user. Please try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // Show loading state
  if (isLoading && !currentUser) {
    return (
      <Center h="200px">
        <Spinner size="xl" />
      </Center>
    );
  }

  // Show error state if no user found
  if (!isLoading && !currentUser && !error) {
    return (
      <Center h="200px">
        <Box textAlign="center">
          <Heading size="md">User not found</Heading>
          <Button mt={4} onClick={() => navigate('/users')}>
            Back to Users
          </Button>
        </Box>
      </Center>
    );
  }

  return (
    <Box>

      <form onSubmit={handleSubmit(onSubmit)}>
        <VStack spacing={6} align="stretch">
          {/* Basic Information */}
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
            <FormControl isRequired isInvalid={!!errors.employeeCode}>
              <FormLabel>Employee Code</FormLabel>
              <Input
                placeholder="Enter Employee Name"
                {...register('employeeCode', { required: true })}
              />
            </FormControl>

            <FormControl isRequired isInvalid={!!errors.mobile}>
              <FormLabel>Mobile</FormLabel>
              <Input
                placeholder="Enter Mobile Number"
                {...register('mobile', {
                  required: true,
                  pattern: {
                    value: /^\d{10}$/,
                    message: 'Please enter a valid 10-digit mobile number'
                  }
                })}
              />
            </FormControl>
          </SimpleGrid>

          <FormControl isRequired isInvalid={!!errors.fullName}>
            <FormLabel>Full Name</FormLabel>
            <Input
              placeholder="Enter Full Name"
              {...register('fullName', { required: true })}
            />
          </FormControl>

          <FormControl>
            <FormLabel>Status</FormLabel>
            <Select {...register('status')}>
              <option value="Pending">Pending</option>
              <option value="Approved">Approved</option>
            </Select>
          </FormControl>

          {/* Roles and Permissions */}
          <Box mt={6}>
            <Heading size="md" mb={4}>
              Roles
            </Heading>

            <Table variant="unstyled">
              <Thead>
                <Tr>
                  <Th></Th>
                  <Th>Dashboard</Th>
                  <Th>Users</Th>
                  <Th>Cards</Th>
                  <Th>Rule</Th>
                  <Th>Applications</Th>
                </Tr>
              </Thead>
              <Tbody>
                <Tr>
                  <Td fontWeight="bold">View</Td>
                  <Td>
                    <Checkbox
                      isChecked={roles.dashboard.view}
                      onChange={(e) => handleRoleChange('dashboard', 'view', e.target.checked)}
                    />
                  </Td>
                  <Td>
                    <Checkbox
                      isChecked={roles.users.view}
                      onChange={(e) => handleRoleChange('users', 'view', e.target.checked)}
                    />
                  </Td>
                  <Td>
                    <Checkbox
                      isChecked={roles.cards.view}
                      onChange={(e) => handleRoleChange('cards', 'view', e.target.checked)}
                    />
                  </Td>
                  <Td>
                    <Checkbox
                      isChecked={roles.rule.view}
                      onChange={(e) => handleRoleChange('rule', 'view', e.target.checked)}
                    />
                  </Td>
                  <Td>
                    <Checkbox
                      isChecked={roles.applications.view}
                      onChange={(e) => handleRoleChange('applications', 'view', e.target.checked)}
                    />
                  </Td>
                </Tr>
                <Tr>
                  <Td fontWeight="bold">Edit</Td>
                  <Td>
                    <Checkbox
                      isChecked={roles.dashboard.edit}
                      onChange={(e) => handleRoleChange('dashboard', 'edit', e.target.checked)}
                    />
                  </Td>
                  <Td>
                    <Checkbox
                      isChecked={roles.users.edit}
                      onChange={(e) => handleRoleChange('users', 'edit', e.target.checked)}
                    />
                  </Td>
                  <Td>
                    <Checkbox
                      isChecked={roles.cards.edit}
                      onChange={(e) => handleRoleChange('cards', 'edit', e.target.checked)}
                    />
                  </Td>
                  <Td>
                    <Checkbox
                      isChecked={roles.rule.edit}
                      onChange={(e) => handleRoleChange('rule', 'edit', e.target.checked)}
                    />
                  </Td>
                  <Td>
                    <Checkbox
                      isChecked={roles.applications.edit}
                      onChange={(e) => handleRoleChange('applications', 'edit', e.target.checked)}
                    />
                  </Td>
                </Tr>
              </Tbody>
            </Table>
          </Box>

          {/* Form Actions */}
          <Box mt={8} textAlign="right">
            <Button
              colorScheme="gray"
              mr={4}
              onClick={() => navigate('/users')}
            >
              Cancel
            </Button>
            <Button
              colorScheme="blue"
              type="submit"
              isLoading={isLoading}
            >
              Save Changes
            </Button>
          </Box>
        </VStack>
      </form>
    </Box>
  );
};

export default EditUser;
// src/routes/cards/create-card.tsx
import {
    Box,
    Button,
    Container,
    FormControl,
    FormLabel,
    FormErrorMessage,
    Input,
    Textarea,
    Select,
    Heading,
    VStack,
    Grid,
    GridItem,
    Card,
    CardBody,
    CardHeader,
    CardFooter,
    Image,
    Text,
    useToast,
    HStack,
    Icon,
    useColorModeValue,
    Badge,
    Spinner,
  } from '@chakra-ui/react';
  import { useForm } from 'react-hook-form';
  import { useNavigate } from 'react-router-dom';
  import { FiUpload } from 'react-icons/fi';
  import { useCardStore } from '../../store/cardStore';
  import { useRuleStore } from '../../store/ruleStore';
  import { CreateCardData } from '../../types/card';
  import { useState, useEffect } from 'react';

  interface CreateCardFormData {
    name: string;
    subHeader: string;
    description: string;
    imageUrl: string;
    utmLink: string;
    ruleId: string;
    earning: string;
    status: 'active' | 'inactive' | 'pending';
  }

  const CreateCard = () => {
    const navigate = useNavigate();
    const toast = useToast();
    const { createCard, isLoading: isCreating } = useCardStore();
    const { rules, fetchRules, isLoading: isLoadingRules } = useRuleStore();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [selectedImage, setSelectedImage] = useState<string>('');
    const cardBg = useColorModeValue('white', 'gray.800');
    const borderColor = useColorModeValue('gray.200', 'gray.600');

    const {
      register,
      handleSubmit,
      watch,
      setValue,
      formState: { errors },
    } = useForm<CreateCardFormData>({
      defaultValues: {
        name: '',
        subHeader: '',
        description: '',
        imageUrl: '',
        utmLink: '',
        ruleId: '',
        earning: '',
        status: 'active',
      },
    });

    const watchedFields = watch();

    useEffect(() => {
      fetchRules();
    }, [fetchRules]);

   const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  const file = e.target.files?.[0];
  if (file) {
    setSelectedImage(URL.createObjectURL(file)); // for preview
    setValue('imageUrl', file); // store the actual File object
  }
};

    const onSubmit = async (data: CreateCardFormData) => {
      setIsSubmitting(true);
      try {
        const createData: CreateCardData = {
          name: data.name,
          subHeader: data.subHeader,
          description: data.description,
          imageUrl: data.imageUrl || '/placeholder-card.png',
          utmLink: data.utmLink,
          ruleId: data.ruleId,
          earning: data.earning,
          status: data.status,
          benefits: data.description.split('\n').filter(line => line.trim().startsWith('•')).map(line => line.trim()),
        };

        await createCard(createData);
        toast({
          title: 'Card created successfully',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        navigate('/cards');
      } catch (error) {
        toast({
          title: 'Failed to create card',
          description: error instanceof Error ? error.message : 'Unknown error',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      } finally {
        setIsSubmitting(false);
      }
    };

    return (
      <Container maxW="container.xl" py={6}>
        <VStack spacing={6} align="stretch">

          <Grid templateColumns={{ base: '1fr', lg: '1fr 1fr' }} gap={8}>
            <GridItem>
              <form onSubmit={handleSubmit(onSubmit)}>
                <VStack spacing={4} align="stretch">
                  <FormControl isInvalid={!!errors.name} isRequired>
                    <FormLabel>Card Name</FormLabel>
                    <Input
                      {...register('name', { required: 'Card name is required' })}
                      placeholder="Tata Neu Plus HDFC Bank Credit Card"
                      bg={cardBg}
                    />
                    <FormErrorMessage>{errors.name?.message}</FormErrorMessage>
                  </FormControl>

                  <FormControl isInvalid={!!errors.subHeader} isRequired>
                    <FormLabel>Sub Header</FormLabel>
                    <Input
                      {...register('subHeader', { required: 'Sub header is required' })}
                      placeholder="No Joining Fee. No Annual Fee"
                      bg={cardBg}
                    />
                    <FormErrorMessage>{errors.subHeader?.message}</FormErrorMessage>
                  </FormControl>

                  <FormControl isInvalid={!!errors.description} isRequired>
                    <FormLabel>Description</FormLabel>
                    <Textarea
                      {...register('description', { required: 'Description is required' })}
                      placeholder="• 4 free domestic lounge visit per year
  • 2% cashback on partner brands
  • 1% back on spends at utilities
  • 1st year free: $0 save on fee"
                      rows={4}
                      bg={cardBg}
                    />
                    <FormErrorMessage>{errors.description?.message}</FormErrorMessage>
                  </FormControl>

                  <FormControl isInvalid={!!errors.imageUrl}>
                    <FormLabel>Upload Card Image</FormLabel>
                    <HStack>
                      <Input
                        type="file"
                        accept="image/*"
                        onChange={handleImageChange}
                        display="none"
                        id="card-image"
                      />
                      <Button
                        as="label"
                        htmlFor="card-image"
                        leftIcon={<Icon as={FiUpload} />}
                        variant="outline"
                        cursor="pointer"
                      >
                        Choose File
                      </Button>
                      {selectedImage && <Text fontSize="sm">Image selected</Text>}
                    </HStack>
                    <FormErrorMessage>{errors.imageUrl?.message}</FormErrorMessage>
                  </FormControl>

                  <FormControl isInvalid={!!errors.utmLink} isRequired>
                    <FormLabel>UTM Link</FormLabel>
                    <Input
                      {...register('utmLink', { required: 'UTM link is required' })}
                      placeholder="Enter UTM Link"
                      bg={cardBg}
                    />
                    <FormErrorMessage>{errors.utmLink?.message}</FormErrorMessage>
                  </FormControl>

                  <FormControl isInvalid={!!errors.ruleId} isRequired>
                    <FormLabel>Attach Rule</FormLabel>
                    <Select
                      {...register('ruleId', { required: 'Please select a rule' })}
                      placeholder="Select Rule"
                      bg={cardBg}
                    >
                      {isLoadingRules ? (
                        <option>Loading rules...</option>
                      ) : (
                        rules.map((rule) => (
                          <option key={rule.id} value={rule.id}>
                            {rule.name}
                          </option>
                        ))
                      )}
                    </Select>
                    <FormErrorMessage>{errors.ruleId?.message}</FormErrorMessage>
                  </FormControl>

                  <FormControl isInvalid={!!errors.earning} isRequired>
                    <FormLabel>Earning</FormLabel>
                    <Input
                      {...register('earning', { required: 'Earning is required' })}
                      placeholder="Enter Earning"
                      bg={cardBg}
                    />
                    <FormErrorMessage>{errors.earning?.message}</FormErrorMessage>
                  </FormControl>

                  <FormControl isInvalid={!!errors.status} isRequired>
                    <FormLabel>Status</FormLabel>
                    <Select
                      {...register('status', { required: 'Please select a status' })}
                      bg={cardBg}
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="pending">Pending</option>
                    </Select>
                    <FormErrorMessage>{errors.status?.message}</FormErrorMessage>
                  </FormControl>

                  <HStack spacing={4} pt={4}>
                    <Button
                      flex={1}
                      colorScheme="blue"
                      type="submit"
                      isLoading={isSubmitting || isCreating}
                      loadingText="Creating..."
                    >
                      Save
                    </Button>
                    <Button
                      flex={1}
                      variant="outline"
                      onClick={() => navigate('/cards')}
                      isDisabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                  </HStack>
                </VStack>
              </form>
            </GridItem>

            <GridItem>
              <VStack align="stretch" spacing={4}>
                <Text fontSize="sm" fontWeight="medium" color="gray.600">Preview</Text>
                <Card
                  bg={cardBg}
                  overflow="hidden"
                >
                  <CardHeader pb={2}>
                    <VStack align="start" spacing={1}>
                      <HStack justify="space-between" w="full">
                        <Text fontSize="lg" fontWeight="bold">
                          {watchedFields.name || 'TATA Neu Plus HDFC Bank Credit Card'}
                        </Text>
                        <Badge colorScheme="green" variant="subtle">
                          {watchedFields.status?.charAt(0).toUpperCase() + watchedFields.status?.slice(1) || 'Active'}
                        </Badge>
                      </HStack>
                    </VStack>
                  </CardHeader>

                  <CardBody py={4}>
                    <Box
                      position="relative"
                      minH="160px"
                      overflow="hidden"
                      borderRadius="md"
                      bg="#2A1A4A"
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                    >
                      {selectedImage ? (
                        <Image
                          src={selectedImage}
                          alt="Card preview"
                          maxH="160px"
                          objectFit="contain"
                        />
                      ) : (
                        <Text color="white" fontSize="sm">Upload card image to preview</Text>
                      )}
                    </Box>
                    <VStack align="start" mt={4} spacing={1}>
                      <Text fontSize="sm" fontWeight="semibold">
                        {watchedFields.subHeader || 'No Joining Fee. No Annual Fee'}
                      </Text>
                      <VStack align="start" spacing={0.5} fontSize="xs" color="gray.600">
                        {watchedFields.description?.split('\n').map((line, index) => (
                          line.trim() && <Text key={index}>{line}</Text>
                        ))}
                      </VStack>
                    </VStack>
                  </CardBody>

                  <CardFooter pt={2}>
                    <Button size="sm" colorScheme="blue" w="full">
                      Apply Now
                    </Button>
                  </CardFooter>
                </Card>
              </VStack>
            </GridItem>
          </Grid>
        </VStack>
      </Container>
    );
  };

  export default CreateCard;
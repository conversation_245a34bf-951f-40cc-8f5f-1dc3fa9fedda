// src/routes/card-management/card-management.tsx
import { useEffect } from 'react';
import {
  Box,
  SimpleGrid,
  Card,
  CardBody,
  Image,
  Stack,
  Text,
  Divider,
  CardFooter,
  ButtonGroup,
  Button,
  Badge,
  Flex,
  Spinner,
  Heading,
  useColorModeValue
} from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { FiPlus, FiCreditCard } from 'react-icons/fi';
import { useCardStore } from '../../store/cardStore';
import { useAuthStore } from '../../store/authStore';

const CardManagement = () => {
  const { cards, isLoading, error, fetchCards } = useCardStore();
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const canEditUsers = user?.permissions?.find(p => p.module_name === 'cards')?.can_edit;

  useEffect(() => {
    fetchCards();
  }, [fetchCards]);

  const handleEdit = (id: string) => {
    navigate(`/cards/edit/${id}`);
  };

  // View functionality removed as requested

  // Delete and status toggle functionality removed as requested

  if (isLoading && cards.length === 0) {
    return (
      <Flex justify="center" align="center" height="400px">
        <Spinner size="xl" color="brand.500" />
      </Flex>
    );
  }

  if (error) {
    return (
      <Box>
        <Heading mb={6}>Card Management</Heading>
        <Card bg="red.50">
          <CardBody>
            <Text color="red.500">Error loading cards: {error}</Text>
          </CardBody>
        </Card>
      </Box>
    );
  }

  const handleCreateCard = () => {
    navigate('/cards/create');
  };

  const cardBg = useColorModeValue('white', 'gray.700');
  const hoverBg = useColorModeValue('gray.50', 'gray.600');

  return (
    <Box>
      {canEditUsers && (
        <Flex justifyContent="flex-end" mb={6}>
          <Button
            colorScheme="brand"
            onClick={handleCreateCard}
            leftIcon={<FiPlus />}
          >
            Create New Card
          </Button>
        </Flex>
      )}

      <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
        {cards.map(card => (
          <Card
            key={card.id}
            maxW="sm"
            bg={cardBg}
            boxShadow="sm"
            borderRadius="lg"
            transition="all 0.2s"
            _hover={{
              transform: 'translateY(-2px)',
              boxShadow: 'md'
            }}
            overflow="hidden"
          >
            <CardBody p={0}>
              <Image
                src={card.image || '/placeholder-card.png'}
                alt={card.title}
                w="100%"
                h="160px"
                objectFit="cover"
                fallbackSrc="https://via.placeholder.com/300x200?text=Card+Image"
              />
              <Box p={4}>
                <Stack spacing={3}>
                  <Flex justify="space-between" align="center">
                    <Heading size="md" noOfLines={1}>{card.title}</Heading>
                    <Badge
                      colorScheme={card.status === 'active' ? 'green' : 'gray'}
                      variant="subtle"
                      px={2}
                      py={1}
                      borderRadius="full"
                    >
                      {card.status}
                    </Badge>
                  </Flex>
                  <Text noOfLines={2} fontSize="sm" color="gray.600">{card.description}</Text>
                </Stack>
              </Box>
            </CardBody>
            <Divider />
            <CardFooter bg={hoverBg} py={3} px={4}>
              <ButtonGroup spacing="2" width="100%">
                {canEditUsers && (
                <Button
                  variant="solid"
                  colorScheme="brand"
                  size="sm"
                  onClick={() => handleEdit(card.id)}
                  width="100%"
                  leftIcon={<FiCreditCard />}
                >
                  Edit Details
                </Button>
                )}
              </ButtonGroup>
            </CardFooter>
          </Card>
        ))}
      </SimpleGrid>

      {cards.length === 0 && (
        <Card bg={hoverBg} borderRadius="lg" boxShadow="sm">
          <CardBody textAlign="center" py={10}>
            <Box
              bg="gray.100"
              w="60px"
              h="60px"
              borderRadius="full"
              display="flex"
              alignItems="center"
              justifyContent="center"
              mx="auto"
              mb={4}
            >
              <FiCreditCard size={24} color="#718096" />
            </Box>
            <Heading size="md" mb={2} color="gray.700">No Cards Found</Heading>
            <Text color="gray.500" mb={6}>Create your first card to get started</Text>
            <Button
              colorScheme="brand"
              onClick={handleCreateCard}
              leftIcon={<FiPlus />}
            >
              Create Your First Card
            </Button>
          </CardBody>
        </Card>
      )}
    </Box>
  );
};

export default CardManagement;
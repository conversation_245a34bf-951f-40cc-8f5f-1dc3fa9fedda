import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Grid,
  Heading,
  Text,
  Flex,
  Divider,
  useColorModeValue,
  <PERSON>ner,
  <PERSON>ge,
  <PERSON>ack,
  Step,
  StepIcon,
  StepIndicator,
  StepNumber,
  StepSeparator,
  StepStatus,
  Stepper,
  useSteps
} from '@chakra-ui/react';
// import MainLayout from '../components/layout/MainLayout';
import { useApplicationStore } from '../store/applicationStore';

// Define the application type based on mockup data fields
type ApplicationDetail = {
  id: string;
  fullName: string;
  employmentType: string;
  mobile: string;
  salaryRange: string;
  pan: string;
  creditScore: number;
  pincode: string;
  cardApplied: string;
  merchantCode: string;
  applicationStatus: string;
  currentStep: number;
  steps: Array<{
    label: string;
    status: 'complete' | 'current' | 'incomplete';
  }>;
};

const ViewDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getApplicationById, isLoading } = useApplicationStore();
  const bgColor = useColorModeValue('gray.50', 'gray.700');

  // Get application details from store
  const application = getApplicationById(id);

  // Define application status steps based on the mockup
  const steps = [
    { label: 'Application Started', status: 'complete' },
    { label: 'Bureau Check', status: 'complete' },
    { label: 'Card Offered', status: 'current' },
    { label: 'UTM Applied', status: 'incomplete' },
    { label: 'Approved Card Out', status: 'incomplete' },
    { label: 'Rejected', status: 'incomplete' }
  ];

  // Set active step based on current application status
  const { activeStep } = useSteps({
    index: application?.currentStep || 2,
    count: steps.length,
  });

  // Redirect to applications list if ID is not found
  useEffect(() => {
    if (!isLoading && !application) {
      navigate('/applications');
    }
  }, [application, isLoading, navigate]);

  if (isLoading) {
    return (

        <Flex justify="center" align="center" height="500px">
          <Spinner size="xl" />
        </Flex>

    );
  }

  if (!application) {
    return null;
  }

  return (

      <Box p={4}>

        <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)' }} gap={6}>
          {/* Full Name */}
          <Box>
            <Text fontSize="sm" color="gray.500">Full Name</Text>
            <Box p={2} bg={bgColor} borderRadius="md" mt={1}>
              <Text>{application.fullName}</Text>
            </Box>
          </Box>

          {/* Employment Type */}
          <Box>
            <Text fontSize="sm" color="gray.500">Employment Type</Text>
            <Box p={2} bg={bgColor} borderRadius="md" mt={1}>
              <Text>{application.employmentType}</Text>
            </Box>
          </Box>

          {/* Mobile */}
          <Box>
            <Text fontSize="sm" color="gray.500">Mobile</Text>
            <Box p={2} bg={bgColor} borderRadius="md" mt={1}>
              <Text>{application.mobile}</Text>
            </Box>
          </Box>

          {/* Salary Range */}
          <Box>
            <Text fontSize="sm" color="gray.500">Salary Range</Text>
            <Box p={2} bg={bgColor} borderRadius="md" mt={1}>
              <Text>{application.salaryRange}</Text>
            </Box>
          </Box>

          {/* PAN */}
          <Box>
            <Text fontSize="sm" color="gray.500">PAN</Text>
            <Box p={2} bg={bgColor} borderRadius="md" mt={1}>
              <Text>{application.pan}</Text>
            </Box>
          </Box>

          {/* Credit Score */}
          <Box>
            <Text fontSize="sm" color="gray.500">Credit Score</Text>
            <Box p={2} bg={bgColor} borderRadius="md" mt={1}>
              <Text>{application.creditScore}</Text>
            </Box>
          </Box>

          {/* Pincode */}
          <Box>
            <Text fontSize="sm" color="gray.500">Pincode</Text>
            <Box p={2} bg={bgColor} borderRadius="md" mt={1}>
              <Text>{application.pincode}</Text>
            </Box>
          </Box>

          {/* Card Applied */}
          <Box>
            <Text fontSize="sm" color="gray.500">Card Applied</Text>
            <Box p={2} bg={bgColor} borderRadius="md" mt={1}>
              <Text>{application.cardApplied}</Text>
            </Box>
          </Box>

          {/* Merchant Code */}
          <Box>
            <Text fontSize="sm" color="gray.500">Merchant Code</Text>
            <Box p={2} bg={bgColor} borderRadius="md" mt={1}>
              <Text>{application.mCode}</Text>
            </Box>
          </Box>
        </Grid>

        <Box mt={8}>
          <Text fontSize="md" fontWeight="bold" mb={4}>Date & Time</Text>

          <Box>
            <Stepper size="sm" index={activeStep} gap="0" colorScheme="blue">
              {steps.map((step, index) => (
                <Step key={index}>
                  <StepIndicator>
                    <StepStatus
                      complete={<StepIcon />}
                      incomplete={<StepNumber />}
                      active={<StepNumber />}
                    />
                  </StepIndicator>
                  <Box flexShrink="0">
                    <Text
                      fontSize="xs"
                      textAlign="center"
                      mt={2}
                      fontWeight={index === activeStep ? "bold" : "normal"}
                    >
                      {step.label}
                    </Text>
                  </Box>
                  <StepSeparator />
                </Step>
              ))}
            </Stepper>
          </Box>
        </Box>
      </Box>

  );
};

export default ViewDetails;
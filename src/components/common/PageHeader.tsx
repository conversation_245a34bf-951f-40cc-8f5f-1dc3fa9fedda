// src/components/common/PageHeader.tsx
import React, { ReactNode } from 'react';
import {
  Box,
  Flex,
  Heading,
  Button,
  HStack,
  useColorModeValue,
  Text,
} from '@chakra-ui/react';

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  actionButton?: {
    label: string;
    onClick: () => void;
    colorScheme?: string;
    leftIcon?: ReactNode;
  };
  children?: ReactNode;
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  actionButton,
  children,
}) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.100', 'gray.700');

  return (
    <Box
      mb={6}
      bg={bgColor}
      borderRadius="lg"
      p={5}
      boxShadow="sm"
      borderWidth="1px"
      borderColor={borderColor}
    >
      <Flex
        justifyContent="space-between"
        alignItems="center"
        flexWrap={{ base: 'wrap', md: 'nowrap' }}
        gap={4}
      >
        <Box>
          <Heading size="lg" color="gray.800">
            {title}
          </Heading>
          {subtitle && (
            <Text mt={1} color="gray.500" fontSize="sm">
              {subtitle}
            </Text>
          )}
        </Box>

        <HStack spacing={3}>
          {children}
          
          {actionButton && (
            <Button
              colorScheme={actionButton.colorScheme || 'brand'}
              onClick={actionButton.onClick}
              leftIcon={actionButton.leftIcon}
              size="md"
            >
              {actionButton.label}
            </Button>
          )}
        </HStack>
      </Flex>
    </Box>
  );
};

export default PageHeader;

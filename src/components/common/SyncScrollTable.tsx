// src/components/common/SyncScrollTable.tsx
import React, { useRef, ReactNode } from 'react';
import {
  Box,
  Table,
  Thead,
  Tbody,
  useColorModeValue,
  TableProps
} from '@chakra-ui/react';

export interface SyncScrollTableProps extends Omit<TableProps, 'children'> {
  // Header content (Tr with Th elements)
  headerContent: ReactNode;
  
  // Body content (rows with Td elements)
  bodyContent: ReactNode;
  
  // Empty state content (shown when no data)
  emptyContent?: ReactNode;
  
  // Loading state
  isLoading?: boolean;
  
  // Custom styles
  containerProps?: any;
  headerProps?: any;
  bodyProps?: any;
  
  // Scroll behavior
  showHorizontalScrollbar?: boolean;
  showVerticalScrollbar?: boolean;
  
  // Table styling
  headerBg?: string;
  maxHeight?: string;
}

export const SyncScrollTable: React.FC<SyncScrollTableProps> = ({
  headerContent,
  bodyContent,
  emptyContent,
  isLoading = false,
  containerProps = {},
  headerProps = {},
  bodyProps = {},
  showHorizontalScrollbar = true,
  showVerticalScrollbar = true,
  headerBg,
  maxHeight = "100%",
  ...tableProps
}) => {
  const headerScrollRef = useRef<HTMLDivElement>(null);
  const bodyScrollRef = useRef<HTMLDivElement>(null);
  
  const defaultHeaderBg = useColorModeValue('white', 'gray.800');
  const finalHeaderBg = headerBg || defaultHeaderBg;

  // Sync horizontal scroll between header and body
  const syncHeaderScroll = (scrollLeft: number) => {
    if (headerScrollRef.current && headerScrollRef.current.scrollLeft !== scrollLeft) {
      headerScrollRef.current.scrollLeft = scrollLeft;
    }
  };

  const syncBodyScroll = (scrollLeft: number) => {
    if (bodyScrollRef.current && bodyScrollRef.current.scrollLeft !== scrollLeft) {
      bodyScrollRef.current.scrollLeft = scrollLeft;
    }
  };

  const handleBodyScroll = (e: React.UIEvent<HTMLDivElement>) => {
    syncHeaderScroll(e.currentTarget.scrollLeft);
  };

  const handleHeaderScroll = (e: React.UIEvent<HTMLDivElement>) => {
    syncBodyScroll(e.currentTarget.scrollLeft);
  };

  return (
    <Box 
      display="flex" 
      flexDirection="column" 
      overflow="hidden"
      maxHeight={maxHeight}
      {...containerProps}
    >
      {/* Fixed Header */}
      <Box 
        ref={headerScrollRef}
        overflowX="auto"
        overflowY="hidden"
        onScroll={handleHeaderScroll}
        sx={{
          /* Hide header scrollbar but keep functionality */
          scrollbarWidth: 'none', /* Firefox */
          msOverflowStyle: 'none', /* IE and Edge */
          '&::-webkit-scrollbar': {
            display: 'none' /* Chrome, Safari, Opera */
          }
        }}
        {...headerProps}
      >
        <Table {...tableProps}>
          <Thead bg={finalHeaderBg}>
            {headerContent}
          </Thead>
        </Table>
      </Box>

      {/* Scrollable Body */}
      <Box
        ref={bodyScrollRef}
        flex="1"
        overflowY={showVerticalScrollbar ? "auto" : "hidden"}
        overflowX={showHorizontalScrollbar ? "auto" : "hidden"}
        onScroll={handleBodyScroll}
        sx={{
          '&::-webkit-scrollbar': {
            width: showVerticalScrollbar ? '6px' : '0',
            height: showHorizontalScrollbar ? '6px' : '0',
            borderRadius: '8px',
            backgroundColor: 'rgba(0, 0, 0, 0.05)',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'rgba(0, 0, 0, 0.1)',
            borderRadius: '8px',
          },
          ...(bodyProps.sx || {})
        }}
        {...bodyProps}
      >
        <Table {...tableProps}>
          <Tbody>
            {bodyContent}
          </Tbody>
        </Table>

        {/* Empty state */}
        {!isLoading && emptyContent && (
          <Box textAlign="center" py={10}>
            {emptyContent}
          </Box>
        )}
      </Box>
    </Box>
  );
};

// Export default
export default SyncScrollTable;
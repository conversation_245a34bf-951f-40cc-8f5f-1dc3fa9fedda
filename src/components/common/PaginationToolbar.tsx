// src/components/common/PaginationToolbar.tsx
import React from 'react';
import {
  Box,
  Button,
  Flex,
  Text,
  Select,
  HStack,
  IconButton,
  useColorModeValue
} from '@chakra-ui/react';
import { FiChevronLeft, FiChevronRight } from "react-icons/fi";
import { UsePaginationReturn } from '../../hooks/usePagination';

interface PaginationToolbarProps {
  pagination: UsePaginationReturn;
  isLoading?: boolean;
  pageSizeOptions?: number[];
  showPageSizeSelector?: boolean;
  showPageInfo?: boolean;
}

export const PaginationToolbar: React.FC<PaginationToolbarProps> = ({
  pagination,
  isLoading = false,
  pageSizeOptions = [10, 15, 25, 50],
  showPageSizeSelector = true,
  showPageInfo = true
}) => {
  const {
    currentPage,
    pageSize,
    totalItems,
    totalPages,
    canGoNext,
    canGoPrevious,
    setPage,
    setPageSize,
    nextPage,
    previousPage,
    getPageNumbers
  } = pagination;

  const buttonBg = useColorModeValue('white', 'gray.700');
  const activeBg = useColorModeValue('blue.500', 'blue.600');
  const activeColor = useColorModeValue('white', 'white');
  const borderColor = useColorModeValue('gray.300', 'gray.600');
  const textColor = useColorModeValue('gray.600', 'gray.400');

  const startItem = totalItems === 0 ? 0 : (currentPage - 1) * pageSize + 1;
  const endItem = Math.min(currentPage * pageSize, totalItems);
  const hasNext = currentPage < totalPages;
  const hasPrevious = currentPage > 1;

  const pageNumbers = getPageNumbers();

  return (
    <Box
      bg={buttonBg}
      p={4}
      borderTop="1px"
      borderColor={borderColor}
      borderBottomRadius="xl"
    >
      <Flex
        justify="space-between"
        align="center"
        flexWrap="wrap"
        gap={4}
      >
        {/* Page Info */}
        {showPageInfo && (
          <Text fontSize="sm" color={textColor} minW="fit-content">
            Showing {startItem} to {endItem} of {totalItems} entries
          </Text>
        )}

        {/* Pagination Controls */}
        <HStack spacing={2}>
          {/* Previous Button */}
          <IconButton
            aria-label="Previous page"
            icon={<FiChevronLeft />}
            size="sm"
            variant="outline"
            onClick={previousPage}
            isDisabled={!hasPrevious || isLoading}
            bg={buttonBg}
            borderColor={borderColor}
          />

          {/* Page Numbers */}
          <HStack spacing={1}>
            {pageNumbers.map((pageNum, index) => (
              pageNum === -1 ? (
                <Text key={`ellipsis-${index}`} px={2} color={textColor}>
                  ...
                </Text>
              ) : (
                <Button
                  key={pageNum}
                  size="sm"
                  variant={currentPage === pageNum ? "solid" : "outline"}
                  colorScheme={currentPage === pageNum ? "blue" : "gray"}
                  onClick={() => setPage(pageNum)}
                  isDisabled={isLoading}
                  bg={currentPage === pageNum ? activeBg : buttonBg}
                  color={currentPage === pageNum ? activeColor : undefined}
                  borderColor={borderColor}
                  minW="32px"
                >
                  {pageNum}
                </Button>
              )
            ))}
          </HStack>

          {/* Next Button */}
          <IconButton
            aria-label="Next page"
            icon={<FiChevronRight />}
            size="sm"
            variant="outline"
            onClick={nextPage}
            isDisabled={!hasNext || isLoading}
            bg={buttonBg}
            borderColor={borderColor}
          />
        </HStack>

        {/* Page Size Selector */}
        {showPageSizeSelector && (
          <HStack spacing={2} minW="fit-content">
            <Text fontSize="sm" color={textColor}>
              Show:
            </Text>
            <Select
              size="sm"
              value={pageSize}
              onChange={(e) => setPageSize(parseInt(e.target.value))}
              isDisabled={isLoading}
              bg={buttonBg}
              borderColor={borderColor}
              width="80px"
            >
              {pageSizeOptions.map(size => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </Select>
          </HStack>
        )}
      </Flex>
    </Box>
  );
};

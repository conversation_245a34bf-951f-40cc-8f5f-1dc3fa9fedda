import React from 'react';
import {
  Box,
  Flex,
  Text,
  Skeleton,
} from '@chakra-ui/react';
import {
  BarC<PERSON>,
  Bar,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  ResponsiveContainer,
} from 'recharts';

// Type for card metrics data
interface CardMetric {
  name: string;
  approved: number;
  total: number;
}

interface CardMetricsChartProps {
  data: CardMetric[];
  isLoading: boolean;
}

const CardMetricsChart: React.FC<CardMetricsChartProps> = ({ data, isLoading }) => {
  if (isLoading) {
    return (
      <Skeleton height="200px" />
    );
  }

  return (
    <Box height="200px">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          layout="vertical"
          data={data}
          margin={{ top: 5, right: 30, left: 50, bottom: 5 }}
          barCategoryGap={24}
          barGap={0}
        >
          <XAxis 
            type="number" 
            domain={[0, 60]} 
            axisLine={false} 
            tickLine={false}
          />
          <YAxis 
            type="category" 
            dataKey="name" 
            tick={{ fontSize: 14 }}
            width={60}
            axisLine={false}
            tickLine={false}
          />
          <Bar 
            dataKey="total" 
            fill="#3182CE" 
            radius={[0, 4, 4, 0]} 
            barSize={16}
            name="Total"
          />
          <Bar 
            dataKey="approved" 
            fill="#68D391" 
            radius={[0, 4, 4, 0]} 
            barSize={16}
            name="Approved"
          />
        </BarChart>
      </ResponsiveContainer>
    </Box>
  );
};

export default CardMetricsChart;
import React from 'react';
import {
  Box,
  Flex,
  Stat,
  StatLabel,
  StatNumber,
  useColorModeValue,
  Skeleton,
} from '@chakra-ui/react';
import { ResponsiveContainer, LineChart, Line } from 'recharts';

interface StatsCardProps {
  title: string;
  stat: string;
  icon: React.ReactNode;
  trend: Array<{ value: number }>;
  trendColor: string;
  isLoading?: boolean;
}

const StatsCard: React.FC<StatsCardProps> = ({ 
  title, 
  stat, 
  icon, 
  trend, 
  trendColor,
  isLoading = false,
}) => {
  return (
    <Stat
      px={6}
      py={5}
      bg="white"
      borderRadius="lg"
      boxShadow="sm"
      position="relative"
    >
      <Flex justifyContent="space-between">
        <Box>
          <Flex align="center">
            <Box
              mr={4}
              borderRadius="full"
              bg={`${trendColor}.50`}
              p={3}
              color={`${trendColor}.500`}
            >
              {icon}
            </Box>
            <Box>
              <StatLabel fontWeight="medium" fontSize="md" color="gray.500" isTruncated>
                {title}
              </StatLabel>
              <Skeleton isLoaded={!isLoading} mt={1}>
                <StatNumber fontSize="3xl" fontWeight="bold">
                  {stat}
                </StatNumber>
              </Skeleton>
            </Box>
          </Flex>
        </Box>
      </Flex>
      <Skeleton isLoaded={!isLoading} mt={4} h="50px">
        <Box position="relative" height="50px" mt={4}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={trend}>
              <Line
                type="monotone"
                dataKey="value"
                stroke={trendColor === "blue" ? "#3182CE" : "#48BB78"}
                strokeWidth={2}
                dot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </Box>
      </Skeleton>
    </Stat>
  );
};

export default StatsCard;
// src/components/layout/Header.tsx
import {
  Box,
  Flex,
  Heading,
  Spacer,
  IconButton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Avatar,
  Text,
  HStack,
  Badge,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  useColorModeValue,
  Divider,
  Icon
} from '@chakra-ui/react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../store/authStore';
import { FiChevronRight, FiBell, FiUser, FiSettings, FiLogOut } from 'react-icons/fi';

// Map of route paths to their display names
const routeTitles = {
  '/': 'Dashboard',
  '/cards': 'Card Management',
  '/applications': 'Applications',
  '/users': 'User Management',
  '/rules': 'Rule Management',
};

// Function to get the current page title based on the path
const getPageTitle = (pathname: string): string => {
  // Check for exact matches first
  if (routeTitles[pathname]) {
    return routeTitles[pathname];
  }

  // Check for nested routes
  if (pathname.startsWith('/cards/edit/')) {
    return 'Edit Card';
  }
  if (pathname.startsWith('/cards/create')) {
    return 'Create Card';
  }
  if (pathname.startsWith('/applications/')) {
    return 'Application Details';
  }
  if (pathname.startsWith('/rules/edit/')) {
    return 'Edit Rule Template';
  }
  if (pathname.startsWith('/rules/create')) {
    return 'Create Rule Template';
  }
  if (pathname.startsWith('/users/edit/')) {
    return 'Edit User';
  }
  if (pathname.startsWith('/users/')) {
    return 'User Detail';
  }
  if (pathname.startsWith('/users/create')) {
    return 'Create User';
  }

  // Default fallback
  return 'Card System';
};

// Function to generate breadcrumbs based on the current path
const getBreadcrumbs = (pathname: string) => {
  const paths = pathname.split('/').filter(Boolean);
  let currentPath = '';

  // Define segments that should not be clickable (they require parameters)
  const nonClickableSegments = ['edit', 'view'];

  // Define routes that require parameters
  const routesRequiringParams = [
    '/users/edit',
    '/cards/edit',
    '/rules/edit',
    '/applications/view'
  ];

  return paths.map((path, index) => {
    currentPath += `/${path}`;

    // Check if this is a non-clickable segment
    // 1. Check if it's in the non-clickable list and followed by a parameter
    // 2. Check if the current path is a route that requires parameters
    const isFollowedByParam = index < paths.length - 1 &&
                             paths[index + 1]?.match(/^[0-9a-fA-F]{24}$/);
    const isNonClickable = (nonClickableSegments.includes(path) && isFollowedByParam) ||
                          routesRequiringParams.includes(currentPath);

    // Handle special cases for IDs in paths
    if (path.match(/^[0-9a-fA-F]{24}$/)) {
      // This is likely an ID, use the parent path's title
      const parentPath = pathname.substring(0, pathname.lastIndexOf('/'));
      const title = getPageTitle(parentPath);
      return {
        path: currentPath,
        label: title,
        clickable: true
      };
    }

    return {
      path: currentPath,
      label: path.charAt(0).toUpperCase() + path.slice(1).replace(/-/g, ' '),
      clickable: !isNonClickable
    };
  });
};

const Header = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { logout, user } = useAuthStore();
  const pageTitle = getPageTitle(location.pathname);
  const breadcrumbs = getBreadcrumbs(location.pathname);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const bgColor = useColorModeValue('white', 'gray.800'); // White background

  return (
    <Box>
      <Flex
        as="header"
        align="center"
        justify="space-between"
        py={3}
        px={6}
        bg={bgColor}
        borderBottom="none"
        h="64px"
        boxShadow="0 1px 3px rgba(0, 0, 0, 0.05)"
        position="relative"
        _after={{
          content: '""',
          position: 'absolute',
          bottom: 0,
          left: '5%',
          width: '90%',
          height: '1px',
          bg: 'gray.100',
        }}
      >
        <HStack spacing={4}>
          <Heading size="md" color="gray.800" display={{ base: 'none', md: 'block' }}>
            {pageTitle}
          </Heading>
        </HStack>

        <Spacer />

        <HStack spacing={3}>
          <IconButton
            aria-label="Notifications"
            icon={<FiBell />}
            variant="ghost"
            color="gray.600"
            _hover={{ bg: 'gray.100' }}
            fontSize="20px"
            size="md"
          />

          <Menu>
            <MenuButton
              as={IconButton}
              icon={
                <HStack spacing={2}>
                  <Avatar
                    size="sm"
                    name={user?.name}
                    bg="brand.500" // Keep the primary blue for the avatar
                    color="white"
                  />
                  <Text
                    display={{ base: 'none', md: 'block' }}
                    fontWeight="medium"
                    fontSize="sm"
                  >
                    {user?.name || 'User'}
                  </Text>
                </HStack>
              }
              variant="ghost"
              px={2}
              color="gray.700"
              _hover={{ bg: 'gray.100' }}
            />
            <MenuList shadow="lg" py={2}>
              <Box px={4} py={2} mb={2}>
                <Text fontWeight="medium">{user?.name}</Text>
                <Text fontSize="sm" color="gray.500">{user?.email || '<EMAIL>'}</Text>
              </Box>
              <Divider mb={2} />
              <MenuItem icon={<Icon as={FiUser} />}>Profile</MenuItem>
              <MenuItem icon={<Icon as={FiSettings} />}>Settings</MenuItem>
              <Divider my={2} />
              <MenuItem
                icon={<Icon as={FiLogOut} />}
                onClick={handleLogout}
                color="red.500"
              >
                Logout
              </MenuItem>
            </MenuList>
          </Menu>
        </HStack>
      </Flex>

      {/* Breadcrumb navigation */}
      <Flex
        px={6}
        py={2}
        bg="white"
        borderBottom="1px"
        borderColor="gray.200"
        fontSize="sm"
      >
        <Breadcrumb separator={<Icon as={FiChevronRight} color="gray.400" />}>
          <BreadcrumbItem>
            <BreadcrumbLink href="/" color="gray.500">Home</BreadcrumbLink>
          </BreadcrumbItem>

          {breadcrumbs.map((crumb, index) => (
            <BreadcrumbItem key={index} isCurrentPage={index === breadcrumbs.length - 1}>
              {crumb.clickable ? (
                <BreadcrumbLink
                  href={crumb.path}
                  color={index === breadcrumbs.length - 1 ? 'brand.500' : 'gray.500'}
                  fontWeight={index === breadcrumbs.length - 1 ? 'medium' : 'normal'}
                >
                  {crumb.label}
                </BreadcrumbLink>
              ) : (
                <Text
                  color={index === breadcrumbs.length - 1 ? 'brand.500' : 'gray.500'}
                  fontWeight={index === breadcrumbs.length - 1 ? 'medium' : 'normal'}
                >
                  {crumb.label}
                </Text>
              )}
            </BreadcrumbItem>
          ))}
        </Breadcrumb>
      </Flex>
    </Box>
  );
};

export default Header;
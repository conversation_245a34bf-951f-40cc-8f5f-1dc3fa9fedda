import { ReactNode } from 'react';
import { Box, Flex, useColorModeValue } from '@chakra-ui/react';
import Header from './Header';
import Sidebar from './Sidebar';

interface MainLayoutProps {
  children: ReactNode;
}

const MainLayout = ({ children }: MainLayoutProps) => {
  const bgColor = useColorModeValue('gray.50', 'gray.900');

  return (
    <Flex height="100vh" overflow="hidden">
      {/* Sidebar */}
      <Sidebar />

      {/* Main Content Area */}
      <Box
        flex="1"
        overflow="auto"
        bg={bgColor}
        display="flex"
        flexDirection="column"
        borderLeft="none"
      >
        {/* Header */}
        <Header />

        {/* Page Content */}
        <Box
          as="main"
          p={8}
          flex="1"
          overflowY="auto"
          sx={{
            '&::-webkit-scrollbar': {
              width: '6px',
              borderRadius: '8px',
              backgroundColor: 'rgba(0, 0, 0, 0.05)',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
              borderRadius: '8px',
            },
          }}
        >
          {children}
        </Box>
      </Box>
    </Flex>
  );
};

export default MainLayout;
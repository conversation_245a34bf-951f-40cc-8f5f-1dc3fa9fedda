// src/components/templates/TemplateForm.tsx
import React from 'react';
import {
  FormControl,
  FormLabel,
  Input,
  Select,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  FormErrorMessage,
  Box,
} from '@chakra-ui/react';
import { UploadIcon } from '@chakra-ui/icons';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import { RuleField, TemplateFormData } from '../../types/template';

interface TemplateFormProps {
  rules: RuleField[];
  onRuleChange: (index: number, field: keyof RuleField, value: string) => void;
  register: UseFormRegister<TemplateFormData>;
  errors: FieldErrors<TemplateFormData>;
  defaultTemplateNumber?: string;
  isEdit?: boolean;
}

const TemplateForm: React.FC<TemplateFormProps> = ({
  rules,
  onRuleChange,
  register,
  errors,
  defaultTemplateNumber,
  isEdit = false,
}) => {
  const handleFileUpload = (index: number) => {
    // Placeholder for file upload functionality
    console.log('Upload pincode file for rule at index:', index);
  };

  return (
    <Box>
      <FormControl mb={6} isInvalid={!!errors.templateNumber}>
        <FormLabel>Template Number</FormLabel>
        <Input
          placeholder={isEdit ? "Named - xxxx" : "Enter Template Name"}
          defaultValue={defaultTemplateNumber}
          {...register('templateNumber', { required: 'Template number is required' })}
        />
        <FormErrorMessage>{errors.templateNumber?.message}</FormErrorMessage>
      </FormControl>

      <Table variant="unstyled" size="md">
        <Thead>
          <Tr>
            <Th>Rule Name</Th>
            <Th>Condition</Th>
            <Th>Value</Th>
            <Th>Action</Th>
          </Tr>
        </Thead>
        <Tbody>
          {rules.map((rule, index) => (
            <Tr key={index}>
              <Td fontWeight="medium">{rule.name}</Td>
              <Td>
                <Select
                  value={rule.condition}
                  onChange={(e) => onRuleChange(index, 'condition', e.target.value)}
                  width="100px"
                >
                  <option value="=">=</option>
                  <option value=">">&gt;</option>
                  <option value="<">&lt;</option>
                  <option value=">=">&gt;=</option>
                  <option value="<=">&lt;=</option>
                </Select>
              </Td>
              <Td>
                {renderValueField(rule, index, onRuleChange, handleFileUpload)}
              </Td>
              <Td>
                <Select
                  value={rule.action}
                  onChange={(e) => onRuleChange(index, 'action', e.target.value)}
                  width="100px"
                >
                  <option value="Hard">Hard</option>
                  <option value="Soft">Soft</option>
                </Select>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Box>
  );
};

const renderValueField = (
  rule: RuleField,
  index: number,
  onRuleChange: (index: number, field: keyof RuleField, value: string) => void,
  handleFileUpload: (index: number) => void
) => {
  switch (rule.name) {
    case 'Gender':
      return (
        <Select
          value={rule.value}
          onChange={(e) => onRuleChange(index, 'value', e.target.value)}
          width="100px"
        >
          <option value="M">M</option>
          <option value="F">F</option>
          <option value="Other">Other</option>
        </Select>
      );
    
    case 'Credit Score Requirement':
      return (
        <Select
          value={rule.value}
          onChange={(e) => onRuleChange(index, 'value', e.target.value)}
          width="100px"
        >
          <option value="A">A</option>
          <option value="B">B</option>
          <option value="C">C</option>
          <option value="D">D</option>
          <option value="F">F</option>
        </Select>
      );
    
    case 'Pincode':
      return (
        <Button
          leftIcon={<UploadIcon />}
          variant="outline"
          size="sm"
          onClick={() => handleFileUpload(index)}
        >
          Upload
        </Button>
      );
    
    default:
      return (
        <Input
          placeholder="Enter Value"
          value={rule.value}
          onChange={(e) => onRuleChange(index, 'value', e.target.value)}
          width="200px"
        />
      );
  }
};

export default TemplateForm;


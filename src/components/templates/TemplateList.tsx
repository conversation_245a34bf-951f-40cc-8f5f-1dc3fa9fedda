// src/components/templates/TemplateList.tsx
import React from 'react';
import {
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  TableContainer,
  Text,
  Skeleton,
} from '@chakra-ui/react';
import { Link as RouterLink } from 'react-router-dom';
import { Template } from '@/types/rules';

interface TemplateListProps {
  templates: Template[];
  isLoading: boolean;
}

const TemplateList: React.FC<TemplateListProps> = ({ templates, isLoading }) => {
  if (isLoading) {
    return (
      <TableContainer>
        <Table variant="unstyled" size="md">
          <Thead>
            <Tr>
              <Th>Date</Th>
              <Th>Template Name</Th>
              <Th>Card Linked</Th>
              <Th>Action</Th>
            </Tr>
          </Thead>
          <Tbody>
            {[1, 2, 3].map((item) => (
              <Tr key={item}>
                <Td><Skeleton height="20px" /></Td>
                <Td><Skeleton height="20px" /></Td>
                <Td><Skeleton height="20px" /></Td>
                <Td><Skeleton height="30px" width="80px" /></Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </TableContainer>
    );
  }

  if (templates.length === 0) {
    return (
      <TableContainer>
        <Table variant="unstyled" size="md">
          <Thead>
            <Tr>
              <Th>Date</Th>
              <Th>Template Name</Th>
              <Th>Card Linked</Th>
              <Th>Action</Th>
            </Tr>
          </Thead>
          <Tbody>
            <Tr>
              <Td colSpan={4} textAlign="center" py={8}>
                <Text fontSize="md" color="gray.500">
                  No templates found. Create your first template!
                </Text>
              </Td>
            </Tr>
          </Tbody>
        </Table>
      </TableContainer>
    );
  }

  return (
    <TableContainer>
      <Table variant="unstyled" size="md">
        <Thead>
          <Tr>
            <Th>Date</Th>
            <Th>Template Name</Th>
            <Th>Card Linked</Th>
            <Th>Action</Th>
          </Tr>
        </Thead>
        <Tbody>
          {templates.map((template) => (
            <Tr key={template.id}>
              <Td>{template.date}</Td>
              <Td>{template.name}</Td>
              <Td>{template.cardLinked}</Td>
              <Td>
                <Button
                  as={RouterLink}
                  to={`/rules/edit-template/${template.id}`}
                  variant="outline"
                  colorScheme="blue"
                  size="sm"
                >
                  Details
                </Button>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};

export default TemplateList;

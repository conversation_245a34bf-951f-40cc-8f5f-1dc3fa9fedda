import { createIcon } from "@chakra-ui/react"

const CardsIcon = createIcon({
  displayName: "CardsIcon",
  viewBox: "0 0 32 32",
  path: (
    <>
      <path 
        d="M20.2927 8.73438H7.70602C7.33268 8.73438 6.98602 8.74771 6.66602 8.76104C3.50602 8.94771 2.66602 10.1077 2.66602 13.7077V14.4811C2.66602 15.2144 3.26602 15.8144 3.99935 15.8144H23.9993C24.7327 15.8144 25.3327 15.2144 25.3327 14.4811V13.7077C25.3327 9.73438 24.3193 8.73438 20.2927 8.73438Z" 
        fill="currentColor"
      />
      <path 
        d="M3.99935 17.8125C3.26602 17.8125 2.66602 18.4125 2.66602 19.1458V23.0258C2.66602 26.9992 3.67935 27.9992 7.70602 27.9992H20.2927C24.2527 27.9992 25.2927 27.0392 25.3327 23.2392V19.1458C25.3327 18.4125 24.7327 17.8125 23.9993 17.8125H3.99935ZM9.27935 24.7458H6.99935C6.45268 24.7458 5.99935 24.2925 5.99935 23.7458C5.99935 23.1992 6.45268 22.7458 6.99935 22.7458H9.29268C9.83935 22.7458 10.2927 23.1992 10.2927 23.7458C10.2927 24.2925 9.83935 24.7458 9.27935 24.7458ZM16.7327 24.7458H12.146C11.5993 24.7458 11.146 24.2925 11.146 23.7458C11.146 23.1992 11.5993 22.7458 12.146 22.7458H16.7327C17.2793 22.7458 17.7327 23.1992 17.7327 23.7458C17.7327 24.2925 17.2927 24.7458 16.7327 24.7458Z" 
        fill="currentColor"
      />
      <path 
        d="M29.3361 17.7756V10.7889C29.3361 6.61558 26.9495 4.80225 23.3495 4.80225H11.4427C10.4294 4.80225 9.52275 4.94891 8.72275 5.25558C8.09608 5.48225 7.53608 5.81558 7.08275 6.25558C6.84275 6.48225 7.02941 6.85558 7.37608 6.85558H21.8695C24.8695 6.85558 27.2961 9.28225 27.2961 12.2822V21.8423C27.2961 22.1756 27.6561 22.3623 27.8961 22.1223C28.8161 21.149 29.3361 19.7223 29.3361 17.7756Z" 
        fill="currentColor"
      />
    </>
  ),
})

export default CardsIcon
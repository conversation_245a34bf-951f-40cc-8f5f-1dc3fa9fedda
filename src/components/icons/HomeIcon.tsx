import { createIcon } from "@chakra-ui/react"

const HomeIcon = createIcon({
  displayName: "HomeIcon",
  viewBox: "0 0 32 32",
  path: (
    <path 
      fillRule="evenodd" 
      clipRule="evenodd" 
      d="M17.4741 29.3332H14.5245C9.92917 29.3332 7.63147 29.3332 6.06472 27.9841C4.49796 26.635 4.17301 24.3768 3.52313 19.8601L3.15144 17.277C2.64552 13.7609 2.39256 12.0029 3.11317 10.4998C3.83379 8.99664 5.3676 8.08296 8.43523 6.25558L10.2817 5.15566C13.0674 3.49622 14.4603 2.6665 15.9993 2.6665C17.5384 2.6665 18.9313 3.49622 21.7169 5.15566L23.5635 6.25558C26.6311 8.08296 28.1649 8.99664 28.8855 10.4998C29.6061 12.0029 29.3532 13.7609 28.8472 17.277L28.4756 19.8601C27.8256 24.3768 27.5007 26.635 25.934 27.9841C24.3672 29.3332 22.0695 29.3332 17.4741 29.3332ZM11.1961 20.7374C11.525 20.2937 12.1513 20.2006 12.595 20.5296C13.5661 21.2493 14.7383 21.6662 15.9995 21.6662C17.2607 21.6662 18.4328 21.2493 19.404 20.5296C19.8477 20.2006 20.474 20.2937 20.8028 20.7374C21.1317 21.181 21.0387 21.8074 20.5949 22.1362C19.2984 23.0973 17.7128 23.6662 15.9995 23.6662C14.2861 23.6662 12.7005 23.0973 11.404 22.1362C10.9603 21.8074 10.8672 21.181 11.1961 20.7374Z" 
      fill="currentColor"
    />
  ),
})

export default HomeIcon
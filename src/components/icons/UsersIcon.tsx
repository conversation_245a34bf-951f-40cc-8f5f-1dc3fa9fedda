import { createIcon } from "@chakra-ui/react"

const UsersIcon = createIcon({
  displayName: "UsersIcon",
  viewBox: "0 0 32 32",
  path: (
    <>
      <g clipPath="url(#clip0_120_8021)">
        <path 
          fillRule="evenodd" 
          clipRule="evenodd" 
          d="M6.66602 12.6665C6.66602 9.3528 9.35231 6.6665 12.666 6.6665C15.9797 6.6665 18.666 9.3528 18.666 12.6665C18.666 15.9802 15.9797 18.6665 12.666 18.6665C9.35231 18.6665 6.66602 15.9802 6.66602 12.6665Z" 
          fill="currentColor"
        />
        <path 
          d="M19.1573 16.0843C19.0966 16.1992 19.1224 16.3425 19.2268 16.4199C20.0022 16.9939 20.9618 17.3333 22.0006 17.3333C24.578 17.3333 26.6673 15.244 26.6673 12.6667C26.6673 10.0893 24.578 8 22.0006 8C20.9618 8 20.0022 8.33947 19.2268 8.91351C19.1224 8.99084 19.0966 9.13411 19.1573 9.24905C19.6958 10.2696 20.0006 11.4325 20.0006 12.6667C20.0006 13.9008 19.6958 15.0637 19.1573 16.0843Z" 
          fill="currentColor"
        />
        <path 
          fillRule="evenodd" 
          clipRule="evenodd" 
          d="M6.18885 20.9324C7.832 20.2192 9.98792 20 12.6673 20C15.3489 20 17.5064 20.2196 19.1501 20.9344C20.9407 21.7129 22.0289 23.0391 22.5993 24.9115C22.8666 25.7891 22.2075 26.6667 21.2991 26.6667H4.038C3.12851 26.6667 2.46792 25.7877 2.73608 24.9085C3.30724 23.0359 4.39716 21.7101 6.18885 20.9324Z" 
          fill="currentColor"
        />
        <path 
          d="M19.7572 18.7151C19.2052 18.7493 19.1728 19.4909 19.68 19.7115C21.0698 20.3159 22.1164 21.2056 22.8669 22.3412C23.4828 23.2731 24.4392 23.9999 25.5561 23.9999H27.9302C28.8732 23.9999 29.5612 23.0629 29.2294 22.1476C29.2104 22.0949 29.1905 22.0427 29.17 21.9908C28.7134 20.838 27.9309 19.9893 26.7722 19.4484C25.6844 18.9405 24.3225 18.7309 22.7181 18.6676L22.6917 18.6665H22.6654C21.7204 18.6665 20.7333 18.6544 19.7572 18.7151Z" 
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_120_8021">
          <rect width="32" height="32" fill="white"/>
        </clipPath>
      </defs>
    </>
  ),
})

export default UsersIcon
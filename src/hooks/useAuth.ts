// src/hooks/useAuth.ts
import { useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import { useAuthStore } from '../store/authStore';

// Custom hook to handle authentication in components
export const useAuth = () => {
  const navigate = useNavigate();
  const { 
    user, 
    isAuthenticated, 
    isLoading, 
    error, 
    login, 
    logout 
  } = useAuthStore();

  // Function to handle protected routes
  const requireAuth = (callback?: () => void) => {
    if (!isLoading && !isAuthenticated) {
      navigate('/login');
    } else if (callback && isAuthenticated) {
      callback();
    }
  };

  // Function to redirect authenticated users away from login/register
  const redirectIfAuthenticated = (path = '/dashboard') => {
    if (isAuthenticated) {
      navigate(path);
    }
  };

  // Check if user has required permission
  const hasPermission = (permission: string) => {
    return user?.permissions.includes(permission) || false;
  };

  // Logout and redirect to login
  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout: handleLogout,
    requireAuth,
    redirectIfAuthenticated,
    hasPermission
  };
};

// Authentication guard component for protected routes
export const useAuthGuard = () => {
  const { isAuthenticated, isLoading } = useAuthStore();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, isLoading, navigate]);

  return { isAuthenticated, isLoading };
};
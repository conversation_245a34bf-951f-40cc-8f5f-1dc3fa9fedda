// src/hooks/usePagination.ts
import { useState, useCallback } from 'react';

export interface PaginationState {
  currentPage: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
}

export interface PaginationActions {
  setPage: (page: number) => void;
  setPageSize: (size: number) => void;
  nextPage: () => void;
  previousPage: () => void;
  setTotalItems: (total: number) => void;
  reset: () => void;
}

export interface UsePaginationOptions {
  initialPage?: number;
  initialPageSize?: number;
  onPageChange?: (page: number, pageSize: number) => void;
}

export interface UsePaginationReturn extends PaginationState, PaginationActions {
  canGoNext: boolean;
  canGoPrevious: boolean;
  getPageNumbers: () => number[];
}

export const usePagination = ({
  initialPage = 1,
  initialPageSize = 15,
  onPageChange
}: UsePaginationOptions = {}): UsePaginationReturn => {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [pageSize, setPageSizeState] = useState(initialPageSize);
  const [totalItems, setTotalItemsState] = useState(0);

  const totalPages = Math.ceil(totalItems / pageSize);
  const canGoNext = currentPage < totalPages;
  const canGoPrevious = currentPage > 1;

  const setPage = useCallback((page: number) => {
    const validPage = Math.max(1, Math.min(page, totalPages || 1));
    setCurrentPage(validPage);
    onPageChange?.(validPage, pageSize);
  }, [totalPages, pageSize, onPageChange]);

  const setPageSize = useCallback((size: number) => {
    const newSize = Math.max(1, size);
    setPageSizeState(newSize);
    // Reset to first page when page size changes
    setCurrentPage(1);
    onPageChange?.(1, newSize);
  }, [onPageChange]);

  const nextPage = useCallback(() => {
    if (canGoNext) {
      setPage(currentPage + 1);
    }
  }, [canGoNext, currentPage, setPage]);

  const previousPage = useCallback(() => {
    if (canGoPrevious) {
      setPage(currentPage - 1);
    }
  }, [canGoPrevious, currentPage, setPage]);

  const setTotalItems = useCallback((total: number) => {
    setTotalItemsState(Math.max(0, total));
  }, []);

  const reset = useCallback(() => {
    setCurrentPage(initialPage);
    setPageSizeState(initialPageSize);
    setTotalItemsState(0);
  }, [initialPage, initialPageSize]);

  // Generate page numbers for pagination display
  const getPageNumbers = useCallback((): number[] => {
    const pages: number[] = [];
    const maxVisiblePages = 5;
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is small
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show smart pagination with ellipsis logic
      const startPage = Math.max(1, currentPage - 2);
      const endPage = Math.min(totalPages, currentPage + 2);
      
      if (startPage > 1) {
        pages.push(1);
        if (startPage > 2) {
          pages.push(-1); // Represents ellipsis
        }
      }
      
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
      
      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          pages.push(-1); // Represents ellipsis
        }
        pages.push(totalPages);
      }
    }
    
    return pages;
  }, [currentPage, totalPages]);

  return {
    // State
    currentPage,
    pageSize,
    totalItems,
    totalPages,
    
    // Actions
    setPage,
    setPageSize,
    nextPage,
    previousPage,
    setTotalItems,
    reset,
    
    // Computed
    canGoNext,
    canGoPrevious,
    getPageNumbers
  };
};
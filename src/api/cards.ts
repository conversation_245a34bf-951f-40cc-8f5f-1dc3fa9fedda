// src/api/cards.ts
import { get, post, put, patch, del } from './client';
import { Card, CardInput } from '../types/card';

export const getCards = () => get<Card[]>('/api/cards');

export const getCard = (id: string) => get<Card>(`/api/cards/${id}`);

export const createCard = (data: CardInput) => post<Card>('/api/cards', data);

export const updateCard = (id: string, data: Partial<CardInput>) => 
  put<Card>(`/api/cards/${id}`, data);

export const deleteCard = (id: string) => 
  del<{ message: string }>(`/api/cards/${id}`);

export const updateCardStatus = (id: string, status: string) => 
  patch<Card>(`/api/cards/${id}/status`, { status });
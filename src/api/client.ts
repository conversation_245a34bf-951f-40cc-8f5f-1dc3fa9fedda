// src/api/client.ts
import { useAuthStore } from '../store/authStore';

const BASE_URL = '';  // Empty string for relative URLs

interface FetchOptions extends RequestInit {
  params?: Record<string, string>;
}

// Generic fetch function with authentication
export const fetchApi = async <T>(
  endpoint: string,
  options: FetchOptions = {}
): Promise<T> => {
  const { token } = useAuthStore.getState();
  const { params, ...fetchOptions } = options;

  // Add query parameters if provided
  let url = `${BASE_URL}${endpoint}`;
  if (params) {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      searchParams.append(key, value);
    });
    url += `?${searchParams.toString()}`;
  }

  // Default headers with auth token
  const headers = {
    'Content-Type': 'application/json',
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
    ...options.headers,
  };

  try {
    const response = await fetch(url, {
      ...fetchOptions,
      headers,
    });

    // Check if the response is successful
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message || `API error: ${response.status} ${response.statusText}`
      );
    }

    // Parse JSON response if content exists
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return await response.json();
    }

    return {} as T;
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

// Convenience methods
export const get = <T>(endpoint: string, options?: FetchOptions) =>
  fetchApi<T>(endpoint, { method: 'GET', ...options });

export const post = <T>(endpoint: string, data: unknown, options?: FetchOptions) =>
  fetchApi<T>(endpoint, {
    method: 'POST',
    body: JSON.stringify(data),
    ...options,
  });

export const put = <T>(endpoint: string, data: unknown, options?: FetchOptions) =>
  fetchApi<T>(endpoint, {
    method: 'PUT',
    body: JSON.stringify(data),
    ...options,
  });

export const patch = <T>(endpoint: string, data: unknown, options?: FetchOptions) =>
  fetchApi<T>(endpoint, {
    method: 'PATCH',
    body: JSON.stringify(data),
    ...options,
  });

export const del = <T>(endpoint: string, options?: FetchOptions) =>
  fetchApi<T>(endpoint, { method: 'DELETE', ...options });
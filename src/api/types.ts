// src/api/types.ts
export interface Card {
    id: string;
    title: string;
    description: string;
    status: 'active' | 'inactive' | 'pending';
    image: string;
    templateId: string;
    createdAt: string;
    updatedAt: string;
}

export type CardInput = Omit<Card, 'id' | 'createdAt' | 'updatedAt'>;

export interface Template {
    id: string;
    name: string;
    description: string;
    fields: TemplateField[];
    createdAt: string;
    updatedAt: string;
}

export interface TemplateField {
    id: string;
    name: string;
    type: 'text' | 'number' | 'date' | 'select' | 'checkbox';
    required: boolean;
    options?: string[]; // For select fields
    defaultValue?: string | number | boolean;
}

export type TemplateInput = Omit<Template, 'id' | 'createdAt' | 'updatedAt'>;

export interface Application {
    id: string;
    cardId: string;
    userId: string;
    status: 'pending' | 'approved' | 'rejected';
    data: Record<string, any>;
    submittedAt: string;
    reviewedAt?: string;
    reviewedBy?: string;
}

export type Permission = {
    module_name: string;
    can_view: boolean;
    can_edit: boolean;
};

export type DashboardSummary = {
    weekly: any[]; // adjust type as needed
    current_month_summary: any[];
    monthly_summary: any[];
    total_summary: {
        total_applications: number;
        total_approved: number;
    };
    bank_wise_summary: any[]; // adjust type as needed
};

export interface User {
    id: string;
    name: string;
    email: string;
    role: 'admin' | 'user';
    permissions: Permission[];
    dashboard: DashboardSummary;
}

export interface DashboardStats {
    totalCards: number;
    activeCards: number;
    pendingApplications: number;
    completedApplications: number;
    chartData: {
        name: string;
        applications: number;
        approvals: number;
    }[];
}